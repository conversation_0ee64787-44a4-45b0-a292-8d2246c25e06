"""
Create a visual collage showcasing the experimental film system for applications.
"""

import cv2
import numpy as np
from pathlib import Path
import json
from PIL import Image, ImageDraw, ImageFont
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_video_frames(video_path: Path, num_frames: int = 6) -> list:
    """Extract representative frames from video."""
    frames = []
    
    try:
        cap = cv2.VideoCapture(str(video_path))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames == 0:
            logger.warning(f"No frames found in {video_path}")
            return frames
        
        # Extract frames at regular intervals
        for i in range(num_frames):
            frame_pos = int((i / (num_frames - 1)) * (total_frames - 1))
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
            
            ret, frame = cap.read()
            if ret:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame_rgb)
        
        cap.release()
        logger.info(f"Extracted {len(frames)} frames from {video_path.name}")
        
    except Exception as e:
        logger.error(f"Error extracting frames from {video_path}: {e}")
    
    return frames


def create_system_collage():
    """Create a comprehensive collage of the system."""
    
    # Define collage dimensions
    collage_width = 1920
    collage_height = 1080
    
    # Create base image
    collage = Image.new('RGB', (collage_width, collage_height), (20, 20, 30))
    draw = ImageDraw.Draw(collage)
    
    try:
        # Try to load a font
        title_font = ImageFont.truetype("arial.ttf", 48)
        subtitle_font = ImageFont.truetype("arial.ttf", 24)
        text_font = ImageFont.truetype("arial.ttf", 18)
    except:
        # Fallback to default font
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        text_font = ImageFont.load_default()
    
    # Title section
    title_text = "AI EXPERIMENTAL FILM SYSTEM"
    subtitle_text = "Advanced Screen Splitting • Keyframe Timeline • Gemini 2.5 Flash Analysis"
    
    # Calculate text positions
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (collage_width - title_width) // 2
    
    subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (collage_width - subtitle_width) // 2
    
    # Draw title
    draw.text((title_x, 30), title_text, fill=(255, 255, 255), font=title_font)
    draw.text((subtitle_x, 90), subtitle_text, fill=(150, 200, 255), font=subtitle_font)
    
    # Extract frames from videos
    output_dir = Path("./output")
    video_files = list(output_dir.glob("ultimate_*.mp4"))
    
    all_frames = []
    video_titles = []
    
    for video_file in video_files[:3]:  # Limit to 3 videos
        frames = extract_video_frames(video_file, num_frames=4)
        if frames:
            all_frames.extend(frames)
            # Extract concept from filename
            concept = video_file.stem.replace("ultimate_", "").replace("_", " ").title()
            video_titles.extend([concept] * len(frames))
    
    # Create grid layout for frames
    if all_frames:
        frame_width = 280
        frame_height = 160
        cols = 4
        rows = 3
        
        start_y = 150
        margin_x = (collage_width - (cols * frame_width + (cols - 1) * 20)) // 2
        
        for i, frame in enumerate(all_frames[:12]):  # Limit to 12 frames
            row = i // cols
            col = i % cols
            
            x = margin_x + col * (frame_width + 20)
            y = start_y + row * (frame_height + 40)
            
            # Resize frame
            frame_pil = Image.fromarray(frame)
            frame_resized = frame_pil.resize((frame_width, frame_height), Image.Resampling.LANCZOS)
            
            # Paste frame
            collage.paste(frame_resized, (x, y))
            
            # Add frame border
            draw.rectangle([x-2, y-2, x+frame_width+2, y+frame_height+2], outline=(100, 100, 100), width=2)
            
            # Add label if available
            if i < len(video_titles):
                label_y = y + frame_height + 5
                draw.text((x, label_y), video_titles[i], fill=(200, 200, 200), font=text_font)
    
    # Add feature highlights
    features_y = 700
    features = [
        "🔍 PySceneDetect Multi-Video Analysis",
        "🤖 Gemini 2.5 Flash Content Analysis", 
        "📱 Advanced Screen Splitting (2x2→8x8→Micro)",
        "🩸 Screen Bleeding Effects",
        "🔄 Recursive Splitting (Mirrors)",
        "⚡ Keyframe Timeline System",
        "🎨 Intelligent Effect Progression",
        "📹 45+ Second Duration Films"
    ]
    
    # Draw features in two columns
    col1_x = 100
    col2_x = collage_width // 2 + 50
    
    for i, feature in enumerate(features):
        x = col1_x if i < 4 else col2_x
        y = features_y + (i % 4) * 30
        draw.text((x, y), feature, fill=(255, 255, 255), font=text_font)
    
    # Add technical specs
    specs_y = 850
    specs_text = "Technical: Python • OpenCV • Gemini API • PySceneDetect • Advanced Computer Vision"
    specs_bbox = draw.textbbox((0, 0), specs_text, font=text_font)
    specs_width = specs_bbox[2] - specs_bbox[0]
    specs_x = (collage_width - specs_width) // 2
    
    draw.text((specs_x, specs_y), specs_text, fill=(150, 150, 150), font=text_font)
    
    # Add metadata info
    try:
        metadata_file = output_dir / "ultimate_metadata_digital_consciousness_emergence.json"
        if metadata_file.exists():
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            stats_text = f"Videos Analyzed: {metadata['video_analysis_summary']['total_videos']} • "
            stats_text += f"Scenes: {metadata['video_analysis_summary']['total_scenes']} • "
            stats_text += f"Model: {metadata['gemini_model']}"
            
            stats_bbox = draw.textbbox((0, 0), stats_text, font=text_font)
            stats_width = stats_bbox[2] - stats_bbox[0]
            stats_x = (collage_width - stats_width) // 2
            
            draw.text((stats_x, specs_y + 30), stats_text, fill=(100, 200, 100), font=text_font)
    except:
        pass
    
    # Add decorative elements
    # Corner accents
    accent_color = (0, 255, 150)
    accent_width = 3
    
    # Top-left corner
    draw.line([(0, 0), (50, 0)], fill=accent_color, width=accent_width)
    draw.line([(0, 0), (0, 50)], fill=accent_color, width=accent_width)
    
    # Top-right corner
    draw.line([(collage_width-50, 0), (collage_width, 0)], fill=accent_color, width=accent_width)
    draw.line([(collage_width, 0), (collage_width, 50)], fill=accent_color, width=accent_width)
    
    # Bottom-left corner
    draw.line([(0, collage_height-50), (0, collage_height)], fill=accent_color, width=accent_width)
    draw.line([(0, collage_height), (50, collage_height)], fill=accent_color, width=accent_width)
    
    # Bottom-right corner
    draw.line([(collage_width-50, collage_height), (collage_width, collage_height)], fill=accent_color, width=accent_width)
    draw.line([(collage_width, collage_height-50), (collage_width, collage_height)], fill=accent_color, width=accent_width)
    
    # Save collage
    output_path = Path("./output/system_collage_application.png")
    collage.save(output_path, "PNG", quality=95)
    
    logger.info(f"✅ System collage created: {output_path}")
    logger.info(f"   Dimensions: {collage_width}x{collage_height}")
    logger.info(f"   Frames included: {len(all_frames)}")
    
    return output_path


def create_technical_diagram():
    """Create a technical system diagram."""
    
    diagram_width = 1600
    diagram_height = 900
    
    diagram = Image.new('RGB', (diagram_width, diagram_height), (15, 15, 25))
    draw = ImageDraw.Draw(diagram)
    
    try:
        title_font = ImageFont.truetype("arial.ttf", 36)
        box_font = ImageFont.truetype("arial.ttf", 16)
        small_font = ImageFont.truetype("arial.ttf", 12)
    except:
        title_font = ImageFont.load_default()
        box_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Title
    title = "AI EXPERIMENTAL FILM SYSTEM ARCHITECTURE"
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (diagram_width - title_width) // 2
    draw.text((title_x, 30), title, fill=(255, 255, 255), font=title_font)
    
    # Define system components
    components = [
        {"name": "Video Input\n(Multi-Video)", "pos": (100, 150), "size": (200, 80), "color": (50, 100, 200)},
        {"name": "PySceneDetect\nScene Analysis", "pos": (400, 150), "size": (200, 80), "color": (200, 100, 50)},
        {"name": "Gemini 2.5 Flash\nContent Analysis", "pos": (700, 150), "size": (200, 80), "color": (100, 200, 50)},
        {"name": "Frame Extraction\n& Processing", "pos": (1000, 150), "size": (200, 80), "color": (200, 50, 100)},
        
        {"name": "Screen Splitting\nEffects Engine", "pos": (100, 350), "size": (200, 80), "color": (150, 50, 200)},
        {"name": "Keyframe Timeline\nSystem", "pos": (400, 350), "size": (200, 80), "color": (50, 200, 200)},
        {"name": "Effect Interpolation\n& Blending", "pos": (700, 350), "size": (200, 80), "color": (200, 200, 50)},
        {"name": "Advanced Rendering\nPipeline", "pos": (1000, 350), "size": (200, 80), "color": (200, 100, 200)},
        
        {"name": "Grid Split\n(2x2→8x8)", "pos": (50, 550), "size": (150, 60), "color": (80, 80, 150)},
        {"name": "Irregular Split\n(Diagonal/Circular)", "pos": (250, 550), "size": (150, 60), "color": (80, 150, 80)},
        {"name": "Recursive Split\n(Mirrors)", "pos": (450, 550), "size": (150, 60), "color": (150, 80, 80)},
        {"name": "Micro Fragment\n(100+ Screens)", "pos": (650, 550), "size": (150, 60), "color": (150, 150, 80)},
        {"name": "Screen Bleeding\n(Content Spill)", "pos": (850, 550), "size": (150, 60), "color": (150, 80, 150)},
        
        {"name": "Ultimate Film Output\n45+ Seconds • 4K Quality", "pos": (400, 750), "size": (400, 80), "color": (100, 255, 100)}
    ]
    
    # Draw components
    for comp in components:
        x, y = comp["pos"]
        w, h = comp["size"]
        color = comp["color"]
        
        # Draw box
        draw.rectangle([x, y, x+w, y+h], fill=color, outline=(255, 255, 255), width=2)
        
        # Draw text
        text_bbox = draw.textbbox((0, 0), comp["name"], font=box_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        text_x = x + (w - text_width) // 2
        text_y = y + (h - text_height) // 2
        
        draw.text((text_x, text_y), comp["name"], fill=(255, 255, 255), font=box_font)
    
    # Draw arrows (simplified)
    arrow_color = (200, 200, 200)
    arrow_width = 3
    
    # Horizontal flow arrows
    for i in range(3):
        start_x = 300 + i * 300
        y = 190
        draw.line([(start_x, y), (start_x + 100, y)], fill=arrow_color, width=arrow_width)
        # Arrow head
        draw.polygon([(start_x + 100, y), (start_x + 90, y-5), (start_x + 90, y+5)], fill=arrow_color)
    
    # Vertical arrows
    for i in range(4):
        x = 200 + i * 300
        draw.line([(x, 230), (x, 350)], fill=arrow_color, width=arrow_width)
        draw.polygon([(x, 350), (x-5, 340), (x+5, 340)], fill=arrow_color)
    
    # Save diagram
    diagram_path = Path("./output/system_architecture_diagram.png")
    diagram.save(diagram_path, "PNG", quality=95)
    
    logger.info(f"✅ Technical diagram created: {diagram_path}")
    
    return diagram_path


def main():
    """Create both collage and diagram."""
    logger.info("🎨 Creating system collage for application...")
    
    try:
        # Create main collage
        collage_path = create_system_collage()
        
        # Create technical diagram
        diagram_path = create_technical_diagram()
        
        logger.info(f"\n🎉 COLLAGE CREATION COMPLETE!")
        logger.info(f"📸 Main Collage: {collage_path}")
        logger.info(f"📊 Technical Diagram: {diagram_path}")
        logger.info(f"\nBoth images are ready for your application!")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating collage: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
