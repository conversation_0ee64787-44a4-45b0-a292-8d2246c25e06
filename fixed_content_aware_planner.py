"""
Fixed content-aware LLM film planner with proper scene detection and image analysis.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging
import base64
from dotenv import load_dotenv
from google import genai
from google.genai import types


# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedContentAwarePlanner:
    """Fixed content-aware planner with proper scene detection and image analysis."""
    
    def __init__(self):
        self.client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Initialize effects
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        
        self.color_fx = ColorEffects()
        self.warp_fx = FrameWarpingEffect()
        self.glitch_fx = GlitchEffects()
        self.datamosh_fx = DatamoshEffect()
    
    def detect_scenes_opencv(self, video_path: str, threshold: float = 15.0) -> list:
        """Detect scenes using OpenCV frame differencing."""
        
        logger.info(f"Detecting scenes in {video_path.name} with threshold {threshold}")
        
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            logger.error(f"Could not open video: {video_path}")
            return []
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        scenes = []
        scene_start_frame = 0
        scene_num = 1
        prev_frame = None
        
        # Process every 5th frame for efficiency
        frame_step = 5
        
        for frame_idx in range(0, total_frames, frame_step):
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert to grayscale and blur
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gray = cv2.GaussianBlur(gray, (21, 21), 0)
            
            if prev_frame is None:
                prev_frame = gray
                continue
            
            # Calculate frame difference
            frame_diff = cv2.absdiff(prev_frame, gray)
            mean_diff = np.mean(frame_diff)
            
            # Scene change detected
            if mean_diff > threshold:
                if frame_idx > scene_start_frame + frame_step:
                    scenes.append({
                        "scene": scene_num,
                        "start": scene_start_frame / fps,
                        "end": frame_idx / fps
                    })
                    scene_num += 1
                    scene_start_frame = frame_idx
            
            prev_frame = gray
        
        # Add final scene
        if scene_start_frame < total_frames - frame_step:
            scenes.append({
                "scene": scene_num,
                "start": scene_start_frame / fps,
                "end": total_frames / fps
            })
        
        cap.release()
        logger.info(f"Found {len(scenes)} scenes")
        return scenes
    
    def extract_representative_frames(self, video_path: str, start_time: float, end_time: float, num_frames: int = 3) -> list:
        """Extract representative frames from a video segment."""
        
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return []
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        segment_duration = end_time - start_time
        
        # Calculate frame positions
        frame_positions = []
        if num_frames == 1:
            frame_positions = [start_time + segment_duration / 2]
        else:
            for i in range(num_frames):
                pos = start_time + (segment_duration * i) / (num_frames - 1)
                frame_positions.append(pos)
        
        # Extract frames
        frames = []
        for pos in frame_positions:
            frame_num = int(pos * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
            ret, frame = cap.read()
            
            if ret:
                # Resize for faster processing
                frame_small = cv2.resize(frame, (512, 384))
                # Convert to base64
                _, buffer = cv2.imencode('.jpg', frame_small)
                frame_b64 = base64.b64encode(buffer).decode('utf-8')
                frames.append(frame_b64)
        
        cap.release()
        return frames
    
    def analyze_video_content_with_scenes(self, video_files: list) -> dict:
        """Analyze video content using scene detection and LLM vision."""
        
        logger.info(f"🔍 Analyzing {len(video_files)} videos with scene detection...")
        
        video_analysis = {}
        
        for video_file in video_files:
            logger.info(f"Analyzing: {video_file.name}")
            
            try:
                # Step 1: Detect scenes
                scenes = self.detect_scenes_opencv(video_file, threshold=12.0)
                
                if not scenes:
                    logger.warning(f"No scenes detected in {video_file.name}")
                    continue
                
                # Step 2: Analyze representative scenes (max 3 scenes)
                scenes_to_analyze = scenes[:3] if len(scenes) > 3 else scenes
                scene_analyses = []
                
                for scene in scenes_to_analyze:
                    # Extract 1 representative frame from this scene
                    frames = self.extract_representative_frames(
                        video_file, scene["start"], scene["end"], num_frames=1
                    )
                    
                    if frames:
                        # Analyze this scene with LLM
                        analysis = self._analyze_scene_with_llm(frames[0], video_file.name, scene)
                        scene_analyses.append({
                            "scene_number": scene["scene"],
                            "start": scene["start"],
                            "end": scene["end"],
                            "analysis": analysis
                        })
                
                video_analysis[str(video_file)] = {
                    "filename": video_file.name,
                    "total_scenes": len(scenes),
                    "all_scenes": scenes,
                    "analyzed_scenes": scene_analyses
                }
                
                logger.info(f"✅ Analyzed {video_file.name}: {len(scenes)} scenes, {len(scene_analyses)} analyzed")
                
            except Exception as e:
                logger.error(f"Error analyzing {video_file.name}: {e}")
                continue
        
        return video_analysis
    
    def _analyze_scene_with_llm(self, frame_b64: str, filename: str, scene: dict) -> dict:
        """Analyze a scene using LLM vision with proper format."""
        
        try:
            prompt = f"""Analyze this scene from "{filename}" (Scene {scene['scene']}: {scene['start']:.1f}s-{scene['end']:.1f}s).

Describe in 2-3 sentences:
1. CONTENT: What is happening in this scene? (people, objects, actions, setting)
2. MOOD: What is the emotional tone? (calm, energetic, dramatic, melancholic, etc.)
3. VISUAL: How does it look? (lighting, colors, composition, movement potential)

Be specific and focus on elements relevant for experimental filmmaking."""
            
            # Use the correct Gemini format
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_bytes(
                            mime_type="image/jpeg",
                            data=base64.b64decode(frame_b64),
                        ),
                        types.Part.from_text(text=prompt),
                    ],
                ),
            ]
            
            response = self.client.models.generate_content(
                model="gemini-2.5-flash-preview-04-17",
                contents=contents,
                config=types.GenerateContentConfig(response_mime_type="text/plain")
            )
            
            analysis_text = response.text
            
            # Parse into structured data
            analysis = {
                "content": "Unknown content",
                "mood": "neutral",
                "visual": "standard visuals",
                "raw_analysis": analysis_text
            }
            
            # Simple parsing
            lines = analysis_text.lower().split('\n')
            for line in lines:
                if 'content:' in line or 'happening' in line:
                    analysis["content"] = line.split(':', 1)[-1].strip()
                elif 'mood:' in line or 'emotional' in line:
                    analysis["mood"] = line.split(':', 1)[-1].strip()
                elif 'visual:' in line or 'looks' in line:
                    analysis["visual"] = line.split(':', 1)[-1].strip()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in LLM scene analysis: {e}")
            return {
                "content": "Analysis failed",
                "mood": "unknown",
                "visual": "unknown",
                "raw_analysis": f"Error: {e}"
            }
    
    def create_intelligent_film_plan(self, concept: str, style: str, video_analysis: dict) -> dict:
        """Create an intelligent film plan based on analyzed video content."""
        
        logger.info(f"🎬 Creating intelligent plan for '{concept}' ({style})")
        
        # Create detailed content summary
        content_summary = "ANALYZED VIDEO CONTENT:\n\n"
        
        for i, (video_path, analysis) in enumerate(video_analysis.items(), 1):
            filename = analysis['filename']
            total_scenes = analysis['total_scenes']
            
            content_summary += f"VIDEO {i}: {filename} ({total_scenes} scenes)\n"
            
            for scene_analysis in analysis['analyzed_scenes']:
                scene_num = scene_analysis['scene_number']
                start = scene_analysis['start']
                end = scene_analysis['end']
                content = scene_analysis['analysis']['content']
                mood = scene_analysis['analysis']['mood']
                
                content_summary += f"  Scene {scene_num} ({start:.1f}s-{end:.1f}s): {content} | Mood: {mood}\n"
            
            content_summary += "\n"
        
        prompt = f"""Create an experimental film plan for "{concept}" ({style} style) using the analyzed video content.

{content_summary}

You have access to specific scenes with known content. Create a plan that:

1. MATCHES SCENES TO CONCEPT: Choose specific scenes that best represent "{concept}"
2. CREATES NARRATIVE PROGRESSION: Use the scenes to tell a story about "{concept}"
3. JUSTIFIES CHOICES: Explain why each scene fits the concept
4. SELECTS APPROPRIATE EFFECTS: Match effects to both concept and scene content

Available effects:
- color_bleed: Psychedelic color separation (intensity 0.1-1.0)
- chromatic_aberration: Lens distortion (intensity 0.1-1.0)
- wave_distortion: Liquid-like warping (amplitude 10-50)
- spiral_distortion: Hypnotic swirling (intensity 0.1-1.0)
- kaleidoscope: Symmetrical patterns (segments 3-12)
- digital_noise: Glitch corruption (intensity 0.1-1.0)
- rgb_shift: Color channel displacement (shift_amount 1-20)
- scan_lines: TV static effect (intensity 0.1-1.0)
- datamosh: Motion corruption (intensity 0.1-1.0)

Create 4-6 segments totaling 20-25 seconds:

SEGMENT 1: [concept aspect] using VIDEO X Scene Y ([start]s-[end]s)
Content Match: [why this scene's content fits the concept]
Effect: [effect_name]
Parameters: [parameters]
Duration: [seconds]

Continue for all segments..."""
        
        try:
            response = self.client.models.generate_content(
                model="gemini-2.5-flash-preview-04-17",
                contents=prompt
            )
            
            plan_text = response.text
            logger.info(f"Intelligent plan created:\n{plan_text}")
            
            # Parse the plan
            segments = self._parse_intelligent_plan(plan_text, video_analysis)
            
            return {
                "concept": concept,
                "style": style,
                "plan_text": plan_text,
                "segments": segments,
                "content_analysis": video_analysis
            }
            
        except Exception as e:
            logger.error(f"Error creating intelligent plan: {e}")
            return {"concept": concept, "style": style, "segments": [], "error": str(e)}

    def _parse_intelligent_plan(self, plan_text: str, video_analysis: dict) -> list:
        """Parse intelligent plan into structured segments with robust parsing."""

        segments = []
        video_paths = list(video_analysis.keys())

        # Split into sections by looking for segment markers
        import re

        # Look for segment patterns in the text
        segment_patterns = [
            r'\*\*SEGMENT (\d+):\*\*\s*([^\n]+)',  # **SEGMENT 1:** format
            r'SEGMENT (\d+):\s*([^\n]+)',          # SEGMENT 1: format
            r'\*\s*\*\*([^*]+)\*\*',               # * **Title** format
        ]

        # Try to find segments using different patterns
        found_segments = []

        for pattern in segment_patterns:
            matches = re.finditer(pattern, plan_text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                if len(match.groups()) >= 2:
                    segment_num = match.group(1) if match.group(1).isdigit() else len(found_segments) + 1
                    title = match.group(2).strip()
                else:
                    segment_num = len(found_segments) + 1
                    title = match.group(1).strip()

                # Find the content after this match
                start_pos = match.end()
                # Find next segment or end of text
                next_match = None
                for next_pattern in segment_patterns:
                    next_matches = list(re.finditer(next_pattern, plan_text[start_pos:], re.MULTILINE | re.IGNORECASE))
                    if next_matches:
                        if next_match is None or next_matches[0].start() < next_match.start():
                            next_match = next_matches[0]

                if next_match:
                    content = plan_text[start_pos:start_pos + next_match.start()]
                else:
                    content = plan_text[start_pos:]

                found_segments.append({
                    "number": int(segment_num) if str(segment_num).isdigit() else len(found_segments) + 1,
                    "title": title,
                    "content": content.strip()
                })

        # If no segments found with patterns, try to extract from bullet points
        if not found_segments:
            lines = plan_text.split('\n')
            current_segment = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for bullet points or numbered items
                if (line.startswith('*') or line.startswith('-') or
                    re.match(r'^\d+\.', line) or 'concept aspect' in line.lower()):

                    if current_segment:
                        found_segments.append(current_segment)

                    current_segment = {
                        "number": len(found_segments) + 1,
                        "title": line,
                        "content": ""
                    }
                elif current_segment:
                    current_segment["content"] += line + "\n"

            if current_segment:
                found_segments.append(current_segment)

        # Parse each found segment
        for seg_data in found_segments:
            segment = {
                "description": seg_data["title"],
                "video_path": None,
                "scene_number": None,
                "start_time": 0,
                "end_time": 5,
                "effect": None,
                "parameters": {},
                "duration": 5.0,
                "content_match": ""
            }

            content = seg_data["content"]

            # Extract video and scene information
            video_match = re.search(r'VIDEO (\d+)', content, re.IGNORECASE)
            scene_match = re.search(r'SCENE (\d+)', content, re.IGNORECASE)
            time_match = re.search(r'(\d+(?:\.\d+)?)s[^\d]*(\d+(?:\.\d+)?)s', content)

            if video_match:
                video_num = int(video_match.group(1)) - 1
                if 0 <= video_num < len(video_paths):
                    segment["video_path"] = video_paths[video_num]

            if scene_match:
                segment["scene_number"] = int(scene_match.group(1))

            if time_match:
                segment["start_time"] = float(time_match.group(1))
                segment["end_time"] = float(time_match.group(2))

            # Extract effect information
            effect_patterns = [
                r'Effect:\s*([^\n]+)',
                r'effect:\s*`([^`]+)`',
                r'applying?\s+([a-z_]+)\s+effect',
                r'use\s+([a-z_]+)\s+effect',
            ]

            for pattern in effect_patterns:
                effect_match = re.search(pattern, content, re.IGNORECASE)
                if effect_match:
                    effect_text = effect_match.group(1).strip()
                    # Clean up effect name
                    effect_clean = re.sub(r'[^a-z_]', '', effect_text.lower())
                    if effect_clean:
                        segment["effect"] = effect_clean
                        break

            # Extract parameters
            param_patterns = [
                r'intensity[=\s]*([0-9.]+)',
                r'amplitude[=\s]*([0-9.]+)',
                r'segments[=\s]*([0-9]+)',
                r'shift_amount[=\s]*([0-9]+)',
            ]

            for pattern in param_patterns:
                param_match = re.search(pattern, content, re.IGNORECASE)
                if param_match:
                    param_name = pattern.split('[')[0]
                    segment["parameters"][param_name] = float(param_match.group(1))

            # Extract duration
            duration_match = re.search(r'Duration:\s*([0-9.]+)', content, re.IGNORECASE)
            if duration_match:
                segment["duration"] = float(duration_match.group(1))
            elif time_match:
                segment["duration"] = segment["end_time"] - segment["start_time"]

            # Extract content match
            content_match_patterns = [
                r'Content Match:\s*([^\n]+)',
                r'Justification:\s*([^\n]+)',
                r'represents?\s+([^\n]+)',
            ]

            for pattern in content_match_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    segment["content_match"] = match.group(1).strip()
                    break

            # Set defaults if nothing found
            if not segment["video_path"] and video_paths:
                segment["video_path"] = video_paths[0]  # Default to first video

            if not segment["effect"]:
                # Try to guess effect from content
                if "blur" in content.lower() or "distort" in content.lower():
                    segment["effect"] = "wave_distortion"
                elif "noise" in content.lower() or "glitch" in content.lower():
                    segment["effect"] = "digital_noise"
                elif "color" in content.lower():
                    segment["effect"] = "color_bleed"
                else:
                    segment["effect"] = "chromatic_aberration"  # Safe default

            if not segment["parameters"]:
                segment["parameters"] = {"intensity": 0.5}  # Safe default

            segments.append(segment)

        logger.info(f"Parsed {len(segments)} segments from plan")
        for i, seg in enumerate(segments):
            logger.info(f"  Segment {i+1}: {seg['effect']} on {Path(seg['video_path']).name if seg['video_path'] else 'unknown'}")

        return segments

    def _parse_parameters(self, params_text: str) -> dict:
        """Parse parameter text into dictionary."""

        params = {}

        if "intensity" in params_text.lower():
            try:
                import re
                match = re.search(r'intensity[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["intensity"] = float(match.group(1))
            except:
                params["intensity"] = 0.5

        if "amplitude" in params_text.lower():
            try:
                match = re.search(r'amplitude[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["amplitude"] = float(match.group(1))
            except:
                params["amplitude"] = 20

        if "segments" in params_text.lower():
            try:
                match = re.search(r'segments[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["segments"] = int(match.group(1))
            except:
                params["segments"] = 6

        if "shift_amount" in params_text.lower():
            try:
                match = re.search(r'shift_amount[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["shift_amount"] = int(match.group(1))
            except:
                params["shift_amount"] = 5

        return params

    def execute_intelligent_plan(self, plan: dict) -> list:
        """Execute the intelligent plan using analyzed content."""

        if not plan.get("segments"):
            logger.warning("No segments to execute")
            return []

        from video_analysis.frame_extractor import FrameExtractor
        extractor = FrameExtractor()

        all_processed_frames = []
        fps = 24

        logger.info(f"Executing intelligent plan with {len(plan['segments'])} segments")

        for i, segment in enumerate(plan["segments"]):
            video_path = segment.get("video_path")
            start_time = segment.get("start_time", 0)
            end_time = segment.get("end_time", 5)
            effect = segment.get("effect", "") or ""
            effect = effect.lower() if effect else ""
            params = segment.get("parameters", {})
            duration = segment.get("duration", 5.0)
            content_match = segment.get("content_match", "")

            logger.info(f"\nSegment {i+1}: {segment['description']}")
            logger.info(f"  Video: {Path(video_path).name if video_path else 'Unknown'}")
            logger.info(f"  Scene: {segment.get('scene_number', 'Unknown')}")
            logger.info(f"  Time: {start_time:.1f}s - {end_time:.1f}s")
            logger.info(f"  Content Match: {content_match}")
            logger.info(f"  Effect: {effect}")

            if not video_path or not Path(video_path).exists():
                logger.error(f"Video path not found: {video_path}")
                continue

            try:
                # Extract frames from specific scene
                frames = extractor.extract_frames(Path(video_path), start_time=start_time, end_time=end_time)

                if not frames:
                    logger.warning(f"No frames extracted from scene")
                    continue

                # Convert to frame list
                frame_list = [frame for _, frame in frames]

                # Adjust frame count for target duration
                frames_needed = int(duration * fps)

                if len(frame_list) > frames_needed:
                    indices = np.linspace(0, len(frame_list) - 1, frames_needed, dtype=int)
                    frame_list = [frame_list[i] for i in indices]
                elif len(frame_list) < frames_needed:
                    while len(frame_list) < frames_needed:
                        frame_list.extend(frame_list[:min(len(frame_list), frames_needed - len(frame_list))])
                    frame_list = frame_list[:frames_needed]

                # Apply the effect
                processed_frames = self._apply_effect(frame_list, effect, params)
                all_processed_frames.extend(processed_frames)

                logger.info(f"  ✅ Added {len(processed_frames)} frames")

            except Exception as e:
                logger.error(f"  ❌ Error processing segment: {e}")

        logger.info(f"Total frames: {len(all_processed_frames)} ({len(all_processed_frames)/fps:.1f}s)")
        return all_processed_frames

    def _apply_effect(self, frames: list, effect: str, params: dict) -> list:
        """Apply effect to frames."""

        try:
            if "color_bleed" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.color_fx.apply_color_bleed(f, intensity) for f in frames]

            elif "chromatic_aberration" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.color_fx.apply_chromatic_aberration(f, intensity) for f in frames]

            elif "wave_distortion" in effect:
                amplitude = params.get("amplitude", 20)
                return [self.warp_fx.apply_wave_distortion(f, amplitude) for f in frames]

            elif "spiral_distortion" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.warp_fx.apply_spiral_distortion(f, intensity) for f in frames]

            elif "kaleidoscope" in effect:
                segments = params.get("segments", 6)
                return [self.warp_fx.apply_kaleidoscope(f, segments) for f in frames]

            elif "digital_noise" in effect:
                intensity = params.get("intensity", 0.3)
                return [self.glitch_fx.apply_digital_noise(f, intensity) for f in frames]

            elif "rgb_shift" in effect:
                shift_amount = params.get("shift_amount", 5)
                return [self.glitch_fx.apply_rgb_shift(f, shift_amount) for f in frames]

            elif "scan_lines" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.glitch_fx.apply_scan_lines(f, intensity=intensity) for f in frames]

            elif "datamosh" in effect:
                intensity = params.get("intensity", 0.5)
                return self.datamosh_fx.apply_datamosh(frames, intensity)

            else:
                logger.warning(f"Unknown effect: {effect}")
                return frames

        except Exception as e:
            logger.error(f"Error applying {effect}: {e}")
            return frames

def test_fixed_content_aware_planner():
    """Test the fixed content-aware planner."""

    logger.info("🎬 Testing Fixed Content-Aware LLM Planner")

    try:
        # Initialize planner
        planner = FixedContentAwarePlanner()

        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))[:2]  # Use first 2 videos

        if len(video_files) < 2:
            logger.error("Need at least 2 video files")
            return False

        logger.info(f"Found {len(video_files)} videos for analysis")

        # Step 1: Analyze video content with scene detection
        video_analysis = planner.analyze_video_content_with_scenes(video_files)

        if not video_analysis:
            logger.error("No video analysis completed")
            return False

        # Step 2: Create intelligent plans
        concepts = [
            ("lost memories", "nostalgic"),
            ("urban anxiety", "aggressive")
        ]

        for concept, style in concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎭 Creating intelligent plan for: '{concept}' ({style})")
            logger.info(f"{'='*60}")

            # Create plan based on analyzed scenes
            plan = planner.create_intelligent_film_plan(concept, style, video_analysis)

            if plan.get("error"):
                logger.error(f"Plan creation failed: {plan['error']}")
                continue

            # Execute the intelligent plan
            processed_frames = planner.execute_intelligent_plan(plan)

            if not processed_frames:
                logger.error("No frames generated")
                continue

            # Save result
            output_path = Path(f"./output/intelligent_{concept.replace(' ', '_')}.mp4")
            output_path.parent.mkdir(exist_ok=True)

            # Save as video
            if save_frames_as_video(processed_frames, output_path):
                file_size = output_path.stat().st_size / 1024 / 1024
                duration = len(processed_frames) / 24
                logger.info(f"✅ Created intelligent film: {output_path}")
                logger.info(f"   Duration: {duration:.1f}s, Size: {file_size:.1f}MB")
            else:
                logger.error(f"❌ Failed to save: {output_path}")

        return True

    except Exception as e:
        logger.error(f"Error in fixed content-aware planner test: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_frames_as_video(frames: list, output_path: Path, fps: int = 24) -> bool:
    """Save frames as video."""

    if not frames:
        return False

    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

        if not out.isOpened():
            return False

        for frame in frames:
            frame_resized = cv2.resize(frame, (width, height))
            out.write(frame_resized)

        out.release()
        return output_path.exists()

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False

if __name__ == "__main__":
    test_fixed_content_aware_planner()
