"""
Complete working system using scene_utils.py and proper video creation.
"""

import os
import json
import logging
import cv2
import numpy as np
from pathlib import Path
import base64
from typing import List, Dict, Any
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def analyze_scene_with_gemini(frames_b64: List[str], scene_info: Dict[str, Any]) -> str:
    """Analyze scene content using Gemini vision."""
    try:
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.error("GEMINI_API_KEY not found in environment")
            return "Scene analysis unavailable - no API key"
        
        client = genai.Client(api_key=api_key)
        
        # Use the first frame for analysis
        if not frames_b64:
            return "No frames available for analysis"
        
        frame_b64 = frames_b64[0]
        
        prompt = f"""Analyze this video frame from scene {scene_info.get('scene', 'unknown')} 
(duration: {scene_info.get('end', 0) - scene_info.get('start', 0):.1f}s).

Provide a concise analysis including:
1. Main visual elements and composition
2. Color palette and lighting
3. Movement or action (if any)
4. Mood or atmosphere
5. Technical aspects (camera angle, depth of field, etc.)

Keep the description under 100 words and focus on visual elements that would be relevant for video editing and effects application."""

        # Create the content with image
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=prompt),
                    types.Part.from_bytes(
                        data=base64.b64decode(frame_b64),
                        mime_type="image/jpeg"
                    )
                ]
            )
        ]
        
        response = client.models.generate_content(
            model="gemini-1.5-flash",
            contents=contents
        )
        
        return response.text.strip()
        
    except Exception as e:
        logger.error(f"Gemini analysis failed: {e}")
        return f"Scene {scene_info.get('scene', 'unknown')} - Analysis failed: {str(e)}"


def create_film_plan_with_gemini(concept: str, style: str, scene_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create a film plan using Gemini based on scene analysis."""
    try:
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.error("GEMINI_API_KEY not found in environment")
            return {"error": "No API key"}
        
        client = genai.Client(api_key=api_key)
        
        # Prepare scene context
        scene_descriptions = []
        for scene in scene_analyses:
            desc = f"Scene {scene['scene']}: {scene['analysis']} (Duration: {scene['duration']:.1f}s)"
            scene_descriptions.append(desc)
        
        scene_context = "\n".join(scene_descriptions) if scene_descriptions else "No scene analysis available"
        
        prompt = f"""You are an experimental filmmaker creating a film with the concept "{concept}" in "{style}" style.

AVAILABLE VIDEO CONTENT:
{scene_context}

Create a detailed film plan that uses the actual video content intelligently. Your plan should:
1. Analyze how each scene's content relates to the concept "{concept}"
2. Choose effects that enhance the natural content rather than fighting against it
3. Create a cohesive visual journey that builds on the existing footage

Respond with a JSON object containing an array of segments. Each segment should have:
- segment_id: number
- description: string (what this segment represents conceptually)
- mood: string (emotional tone)
- effects: array of effect names
- duration_weight: number (relative duration, 0.5-2.0)

Available effects:
- color_bleed: Separates color channels for psychedelic effects
- wave_distortion: Flowing liquid effects
- spiral_distortion: Hypnotic swirling
- kaleidoscope: Symmetrical patterns
- digital_noise: Glitch corruption
- rgb_shift: Channel displacement
- chromatic_aberration: Lens-like color separation
- liquid_distortion: Fluid-like flowing distortion
- vortex_distortion: Whirlpool effects
- datamosh: Motion corruption

Create 3-4 segments that tell a cohesive story."""
        
        response = client.models.generate_content(
            model="gemini-1.5-flash",
            contents=[types.Content(role="user", parts=[types.Part.from_text(text=prompt)])],
            config=types.GenerateContentConfig(response_mime_type="application/json")
        )
        
        plan_json = json.loads(response.text)
        return plan_json
        
    except Exception as e:
        logger.error(f"Plan creation failed: {e}")
        return {"error": str(e)}


def apply_effects_to_frames(frames: List[np.ndarray], effects: List[str]) -> List[np.ndarray]:
    """Apply effects to frames."""
    try:
        # Initialize effects
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        
        color_fx = ColorEffects()
        warp_fx = FrameWarpingEffect()
        glitch_fx = GlitchEffects()
        datamosh_fx = DatamoshEffect()
        
        processed_frames = frames.copy()
        
        for effect in effects:
            logger.info(f"Applying effect: {effect}")
            
            if effect == "color_bleed":
                processed_frames = [color_fx.apply_color_bleed(f, 0.5) for f in processed_frames]
            elif effect == "wave_distortion":
                processed_frames = [warp_fx.apply_wave_distortion(f, amplitude=20, frequency=0.1) for f in processed_frames]
            elif effect == "spiral_distortion":
                processed_frames = [warp_fx.apply_spiral_distortion(f, 0.5) for f in processed_frames]
            elif effect == "kaleidoscope":
                processed_frames = [warp_fx.apply_kaleidoscope(f, 6) for f in processed_frames]
            elif effect == "digital_noise":
                processed_frames = [glitch_fx.apply_digital_noise(f, 0.3) for f in processed_frames]
            elif effect == "rgb_shift":
                processed_frames = [glitch_fx.apply_rgb_shift(f, 5) for f in processed_frames]
            elif effect == "chromatic_aberration":
                processed_frames = [color_fx.apply_chromatic_aberration(f, 0.5) for f in processed_frames]
            elif effect == "liquid_distortion":
                processed_frames = [warp_fx.apply_liquid_distortion(f, 0.5, 0.0, 0.3) for f in processed_frames]
            elif effect == "vortex_distortion":
                processed_frames = [warp_fx.apply_vortex_distortion(f, 0.5, 0.5, 1.0, 0.5, True) for f in processed_frames]
            elif effect == "datamosh":
                if len(processed_frames) > 1:
                    processed_frames = datamosh_fx.apply_datamosh(processed_frames, 0.5)
            else:
                logger.warning(f"Unknown effect: {effect}")
        
        return processed_frames
        
    except Exception as e:
        logger.error(f"Error applying effects: {e}")
        return frames


def save_frames_as_video(frames: List[np.ndarray], output_path: Path, fps: int = 24) -> bool:
    """Save frames as video with proper codec."""
    if not frames:
        logger.error("No frames to save")
        return False

    try:
        height, width = frames[0].shape[:2]
        
        # Try different codecs
        codecs = ['mp4v', 'XVID', 'MJPG']
        
        for codec in codecs:
            try:
                fourcc = cv2.VideoWriter_fourcc(*codec)
                out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
                
                if out.isOpened():
                    logger.info(f"Using codec: {codec}")
                    
                    for frame in frames:
                        # Ensure frame is in correct format
                        if len(frame.shape) == 3:
                            out.write(frame)
                        else:
                            # Convert grayscale to BGR
                            bgr_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
                            out.write(bgr_frame)
                    
                    out.release()
                    
                    # Verify file was created and has size
                    if output_path.exists() and output_path.stat().st_size > 0:
                        logger.info(f"Video saved successfully: {output_path} ({output_path.stat().st_size} bytes)")
                        return True
                    else:
                        logger.warning(f"Video file created but has zero size with codec {codec}")
                        
            except Exception as e:
                logger.warning(f"Codec {codec} failed: {e}")
                continue
        
        logger.error("All codecs failed")
        return False

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False


def main():
    """Main function to run the complete system."""
    logger.info("🚀 Starting Complete Working System")
    
    try:
        # Import scene detection
        from effects.scene_utils import split_scenes
        
        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found")
            return False
        
        source_video = video_files[0]
        logger.info(f"Using source video: {source_video.name}")
        
        # Step 1: Scene Detection with frame extraction
        logger.info("🔍 Step 1: Scene Detection")
        scenes = split_scenes(
            str(source_video),
            use_opencv=True,  # Use OpenCV method
            extract_frames=True,
            frames_per_segment=3,
            frame_interval=1.0
        )
        
        if not scenes:
            logger.error("No scenes detected")
            return False
        
        logger.info(f"Detected {len(scenes)} scenes")
        
        # Step 2: Content Analysis with Gemini
        logger.info("🤖 Step 2: Content Analysis")
        scene_analyses = []
        
        for scene in scenes[:3]:  # Analyze first 3 scenes
            logger.info(f"Analyzing scene {scene['scene']}")
            
            frames_b64 = scene.get('frames', [])
            if frames_b64:
                analysis = analyze_scene_with_gemini(frames_b64, scene)
                scene_analyses.append({
                    'scene': scene['scene'],
                    'start': scene['start'],
                    'end': scene['end'],
                    'duration': scene['end'] - scene['start'],
                    'analysis': analysis
                })
                logger.info(f"Scene {scene['scene']} analysis: {analysis[:100]}...")
            else:
                logger.warning(f"No frames available for scene {scene['scene']}")
        
        if not scene_analyses:
            logger.error("No scene analyses available")
            return False
        
        # Step 3: Film Planning
        logger.info("📋 Step 3: Film Planning")
        concepts = [
            ("digital consciousness", "cyberpunk"),
            ("liquid memories", "dreamy"),
            ("fractured reality", "glitch")
        ]
        
        # Extract actual frames for processing
        logger.info("📹 Extracting frames for processing")
        from video_analysis.frame_extractor import FrameExtractor
        
        extractor = FrameExtractor()
        frames = extractor.extract_frames(source_video, start_time=0, end_time=5)
        frame_list = [frame for _, frame in frames[:20]]  # Use 20 frames
        
        if not frame_list:
            logger.error("No frames extracted for processing")
            return False
        
        logger.info(f"Extracted {len(frame_list)} frames for processing")
        
        # Create output directory
        output_dir = Path("./output")
        output_dir.mkdir(exist_ok=True)
        
        created_films = []
        
        for concept, style in concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎬 Creating film: '{concept}' ({style})")
            logger.info(f"{'='*60}")
            
            # Create plan
            plan = create_film_plan_with_gemini(concept, style, scene_analyses)
            
            if plan.get('error'):
                logger.error(f"Plan creation failed: {plan['error']}")
                continue
            
            segments = plan.get('segments', [])
            if not segments:
                logger.error("No segments in plan")
                continue
            
            logger.info(f"Plan created with {len(segments)} segments")
            
            # Process segments
            all_processed_frames = []
            total_weight = sum(seg.get('duration_weight', 1.0) for seg in segments)
            
            for segment in segments:
                logger.info(f"Processing segment {segment['segment_id']}: {segment['description']}")
                
                # Calculate frames for this segment
                weight = segment.get('duration_weight', 1.0)
                segment_frames = max(1, int(len(frame_list) * weight / total_weight))
                
                # Get frames for this segment
                start_idx = len(all_processed_frames)
                end_idx = min(start_idx + segment_frames, len(frame_list))
                segment_frame_list = frame_list[start_idx:end_idx]
                
                if not segment_frame_list:
                    continue
                
                # Apply effects
                effects = segment.get('effects', [])
                processed_segment = apply_effects_to_frames(segment_frame_list, effects)
                all_processed_frames.extend(processed_segment)
                
                logger.info(f"Processed {len(processed_segment)} frames with effects: {effects}")
            
            if not all_processed_frames:
                logger.error("No processed frames")
                continue
            
            # Save film
            safe_concept = concept.replace(' ', '_').replace('/', '_')
            output_path = output_dir / f"working_{safe_concept}.mp4"
            
            if save_frames_as_video(all_processed_frames, output_path, fps=24):
                logger.info(f"✅ Created film: {output_path}")
                created_films.append({
                    'concept': concept,
                    'style': style,
                    'path': str(output_path),
                    'frames': len(all_processed_frames)
                })
            else:
                logger.error(f"❌ Failed to save: {output_path}")
        
        # Summary
        logger.info(f"\n{'='*60}")
        logger.info("🎉 SYSTEM COMPLETE!")
        logger.info(f"{'='*60}")
        
        if created_films:
            logger.info(f"Created {len(created_films)} films:")
            for film in created_films:
                logger.info(f"  📽️  {film['concept']}: {film['path']} ({film['frames']} frames)")
            return True
        else:
            logger.error("No films were created")
            return False
            
    except Exception as e:
        logger.error(f"System failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
