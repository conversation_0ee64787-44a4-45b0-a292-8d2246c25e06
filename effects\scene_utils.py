# scene_utils.py
# Splits a video into scenes using PySceneDetect and returns scene metadata
import subprocess
import csv
import os
import logging
import cv2
import numpy as np
import math
import base64
from io import BytesIO
from PIL import Image

logger = logging.getLogger(__name__)


def detect_scenes_opencv(input_file: str, threshold=15.0) -> list[dict]:
    """
    Fallback method to detect scenes using OpenCV.
    Uses frame differencing to detect scene changes.

    Args:
        input_file: Path to the video file
        threshold: Threshold for scene change detection (higher = fewer scenes)

    Returns:
        List of scene dictionaries with scene number, start and end times
    """
    logger.info(f"Using OpenCV fallback method for scene detection with threshold {threshold}")

    cap = cv2.VideoCapture(input_file)
    if not cap.isOpened():
        raise ValueError(f"Could not open video file: {input_file}")

    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    scenes = []
    scene_start_frame = 0
    scene_num = 1
    prev_frame = None

    # Process every 5th frame for efficiency
    frame_step = 5

    for frame_idx in range(0, total_frames, frame_step):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if not ret:
            break

        # Convert to grayscale and blur to reduce noise
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        gray = cv2.GaussianBlur(gray, (21, 21), 0)

        if prev_frame is None:
            prev_frame = gray
            continue

        # Calculate absolute difference between current and previous frame
        frame_diff = cv2.absdiff(prev_frame, gray)
        mean_diff = np.mean(frame_diff)

        # If difference is above threshold, we have a scene change
        if mean_diff > threshold:
            # Add previous scene
            if frame_idx > scene_start_frame + frame_step:  # Avoid very short scenes
                scenes.append({
                    "scene": scene_num,
                    "start": scene_start_frame / fps,
                    "end": frame_idx / fps
                })
                scene_num += 1
                scene_start_frame = frame_idx

        prev_frame = gray

    # Add the last scene
    if scene_start_frame < total_frames - frame_step:
        scenes.append({
            "scene": scene_num,
            "start": scene_start_frame / fps,
            "end": total_frames / fps
        })

    cap.release()
    logger.info(f"OpenCV scene detection found {len(scenes)} scenes")
    return scenes


def extract_frames_from_segment(video_path: str, start_time: float, end_time: float,
                            num_frames: int = 3, frame_interval: float = 2.0) -> list[str]:
    """
    Extract frames from a video segment and convert them to base64-encoded strings.

    Args:
        video_path: Path to the video file
        start_time: Start time of the segment in seconds
        end_time: End time of the segment in seconds
        num_frames: Number of frames to extract from the segment
        frame_interval: Time interval between frames in seconds (default: 2.0)

    Returns:
        List of base64-encoded frame images
    """
    logger.info(f"Extracting frames from segment {start_time:.2f}s to {end_time:.2f}s")
    logger.info(f"Parameters: num_frames={num_frames}, frame_interval={frame_interval}s")

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logger.error(f"Could not open video file: {video_path}")
        return []

    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # Calculate frame positions
    segment_duration = end_time - start_time
    frame_positions = []

    # Determine frame extraction method
    if frame_interval > 0:
        # Extract frames at regular intervals
        current_time = start_time
        while current_time < end_time and len(frame_positions) < num_frames:
            frame_positions.append(current_time)
            current_time += frame_interval
    elif num_frames == 1:
        # Just the middle frame
        frame_positions = [start_time + segment_duration / 2]
    else:
        # Evenly spaced frames
        for i in range(num_frames):
            pos = start_time + (segment_duration * i) / (num_frames - 1)
            frame_positions.append(pos)

    # Limit to the requested number of frames
    frame_positions = frame_positions[:num_frames]

    logger.info(f"Will extract {len(frame_positions)} frames at times: {[f'{pos:.2f}s' for pos in frame_positions]}")

    # Extract frames
    frames = []
    for pos in frame_positions:
        # Convert time to frame number
        frame_num = int(pos * fps)

        # Ensure frame number is within bounds
        if frame_num >= total_frames:
            frame_num = total_frames - 1

        # Set position and read frame
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()

        if ret:
            # Convert frame to base64
            _, buffer = cv2.imencode('.jpg', frame)
            img_str = base64.b64encode(buffer).decode('utf-8')
            frames.append(img_str)
            logger.info(f"Extracted frame at {pos:.2f}s")
        else:
            logger.warning(f"Failed to extract frame at {pos:.2f}s")

    cap.release()
    logger.info(f"Extracted {len(frames)} frames")
    return frames


def create_time_segments(input_file: str, segment_length_seconds: int = 30,
                     extract_frames: bool = False, frames_per_segment: int = 15,
                     frame_interval: float = 2.0) -> list[dict]:
    """
    Creates fixed-length time segments from a video without scene detection.
    This is useful when PySceneDetect doesn't find enough scene changes.

    Args:
        input_file: Path to the video file
        segment_length_seconds: Length of each segment in seconds
        extract_frames: Whether to extract frames from each segment
        frames_per_segment: Maximum number of frames to extract per segment
        frame_interval: Time interval between frames in seconds

    Returns:
        List of scene dictionaries with scene number, start and end times, and optionally frames
    """
    logger.info(f"Creating time segments of {segment_length_seconds} seconds each")

    # Open the video file
    cap = cv2.VideoCapture(input_file)
    if not cap.isOpened():
        raise ValueError(f"Could not open video file: {input_file}")

    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration_seconds = total_frames / fps

    # Calculate number of segments
    num_segments = math.ceil(duration_seconds / segment_length_seconds)
    logger.info(f"Video duration: {duration_seconds:.2f} seconds, creating {num_segments} segments")

    # Create segments
    segments = []
    for i in range(num_segments):
        start_time = i * segment_length_seconds
        end_time = min((i + 1) * segment_length_seconds, duration_seconds)

        segment = {
            "scene": i + 1,
            "start": start_time,
            "end": end_time
        }

        # Extract frames if requested
        if extract_frames:
            frames = extract_frames_from_segment(
                input_file,
                start_time,
                end_time,
                num_frames=frames_per_segment,
                frame_interval=frame_interval
            )
            segment["frames"] = frames

        segments.append(segment)

    cap.release()
    logger.info(f"Created {len(segments)} time segments")
    return segments


def split_scenes(input_file: str, bypass_scene_detection: bool = False,
              segment_length: int = 30, extract_frames: bool = False,
              frames_per_segment: int = 15, frame_interval: float = 2.0,
              use_opencv: bool = False) -> list[dict]:
    """
    Runs PySceneDetect on the input file to detect scenes.
    Falls back to OpenCV-based detection if PySceneDetect fails.
    Can bypass scene detection entirely and use time-based segments.

    Args:
        input_file: Path to the video file
        bypass_scene_detection: If True, bypass scene detection and use time segments
        segment_length: Length of each segment in seconds when bypassing scene detection
        extract_frames: Whether to extract frames from each segment
        frames_per_segment: Maximum number of frames to extract per segment
        frame_interval: Time interval between frames in seconds
        use_opencv: Whether to use OpenCV for scene detection instead of PySceneDetect

    Returns:
        List of dicts with keys: scene(int), start(float), end(float), and optionally frames.
    """
    # Check if input file exists
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input video file not found: {input_file}")

    # If bypass_scene_detection is True, use time segments instead
    if bypass_scene_detection:
        logger.info(f"Bypassing scene detection, using {segment_length} second segments")
        logger.info(f"Frame extraction settings: extract_frames={extract_frames}, frames_per_segment={frames_per_segment}, frame_interval={frame_interval}s")
        return create_time_segments(
            input_file,
            segment_length,
            extract_frames,
            frames_per_segment,
            frame_interval
        )

    # If use_opencv is True, use OpenCV directly
    if use_opencv:
        logger.info("Using OpenCV directly for scene detection")
        scenes = detect_scenes_opencv(input_file, threshold=15.0)

        # Extract frames if requested
        if extract_frames:
            for scene in scenes:
                frames = extract_frames_from_segment(
                    input_file,
                    scene["start"],
                    scene["end"],
                    num_frames=frames_per_segment,
                    frame_interval=frame_interval
                )
                scene["frames"] = frames

        return scenes

    # Try PySceneDetect first
    try:
        # Create output directory if it doesn't exist
        output_dir = "."
        csv_file = os.path.join(output_dir, "scenes.csv")

        # Remove existing CSV file if it exists
        if os.path.exists(csv_file):
            os.remove(csv_file)

        # invoke scenedetect to detect-content with a lower threshold and output CSV
        logger.info("Attempting to use PySceneDetect for scene detection with a lower threshold")
        subprocess.run([
            "scenedetect", "--input", input_file,
            "detect-content", "--threshold", "10", "list-scenes",
            "--output", output_dir,
            "--filename", "scenes.csv"
        ], check=True)

        scenes = []
        if not os.path.exists(csv_file):
            logger.warning("PySceneDetect did not create CSV file, falling back to OpenCV")
            return detect_scenes_opencv(input_file)

        # Read the CSV file
        with open(csv_file, newline="", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)
            scene_count = sum(1 for _ in reader)

        # If PySceneDetect found only 1 scene, fall back to time segments
        if scene_count <= 1:
            logger.warning(f"PySceneDetect only found {scene_count} scene(s), using time segments instead")
            return create_time_segments(
                input_file,
                segment_length,
                extract_frames,
                frames_per_segment,
                frame_interval
            )

    except (subprocess.SubprocessError, FileNotFoundError) as e:
        logger.warning(f"PySceneDetect failed: {str(e)}, falling back to OpenCV")
        scenes = detect_scenes_opencv(input_file)

        # Extract frames if requested
        if extract_frames:
            for scene in scenes:
                frames = extract_frames_from_segment(
                    input_file,
                    scene["start"],
                    scene["end"],
                    num_frames=frames_per_segment,
                    frame_interval=frame_interval
                )
                scene["frames"] = frames

        return scenes

    scenes = []
    with open(csv_file, newline="", encoding="utf-8") as csvfile:
        # Skip the first line which is just a timecode list
        next(csvfile)

        # Now read the CSV with the proper header
        reader = csv.DictReader(csvfile)

        for row in reader:
            # Handle different column names in different versions of PySceneDetect
            try:
                scene_num = int(row.get("Scene Number", 0))

                # Get start and end times in seconds directly
                if "Start Time (seconds)" in row and "End Time (seconds)" in row:
                    start_time = float(row["Start Time (seconds)"])
                    end_time = float(row["End Time (seconds)"])
                    logger.info(f"Using seconds format: Scene {scene_num}: Start time = {start_time}, End time = {end_time}")
                else:
                    # Try to find the columns with different names
                    start_time_col = None
                    end_time_col = None

                    # Look for columns that might contain the time in seconds
                    for col in row.keys():
                        if "start" in col.lower() and "second" in col.lower():
                            start_time_col = col
                        if "end" in col.lower() and "second" in col.lower():
                            end_time_col = col

                    if start_time_col and end_time_col:
                        start_time = float(row[start_time_col])
                        end_time = float(row[end_time_col])
                        logger.info(f"Found time columns: {start_time_col}, {end_time_col}")
                    else:
                        logger.warning(f"Could not find Start/End Time (seconds) in row: {list(row.keys())}")
                        start_time = 0
                        end_time = 0
            except (ValueError, KeyError) as e:
                logger.error(f"Error parsing scene data: {e}")
                start_time = 0
                end_time = 0

            logger.info(f"Scene {scene_num}: Start time = {start_time}, End time = {end_time}")

            scene = {
                "scene": scene_num,
                "start": start_time,
                "end": end_time
            }

            # Extract frames if requested
            if extract_frames:
                frames = extract_frames_from_segment(
                    input_file,
                    start_time,
                    end_time,
                    num_frames=frames_per_segment,
                    frame_interval=frame_interval
                )
                scene["frames"] = frames

            scenes.append(scene)
            scene_num += 1

    logger.info(f"PySceneDetect found {len(scenes)} scenes")
    return scenes
