"""
Gemini-based LLM film planner with function calling for effects.
"""

import os
import json
import logging
import base64
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class EffectCall:
    """Represents a function call for an effect."""
    function_name: str
    parameters: Dict[str, Any]
    target_frames: str = "all"  # "all", "first_half", "second_half", "random_subset"
    blend_mode: str = "replace"  # "replace", "overlay", "multiply"


@dataclass
class FilmSegment:
    """Represents a segment of the film with effects."""
    segment_id: int
    description: str
    mood: str
    effects: List[EffectCall]
    duration_weight: float = 1.0  # Relative duration compared to other segments


class GeminiFunctionPlanner:
    """LLM planner that uses function calling to orchestrate effects."""
    
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set")
        
        self.client = genai.Client(api_key=self.api_key)
        
        # Initialize effects
        self._initialize_effects()
        
        # Define available functions for the LLM
        self.available_functions = self._define_effect_functions()
    
    def _initialize_effects(self):
        """Initialize all effect classes."""
        try:
            from effects.color_effects import ColorEffects
            from effects.frame_warping import FrameWarpingEffect
            from effects.glitch_effects import GlitchEffects
            from effects.datamosh import DatamoshEffect
            from effects.recursive_effects import RecursiveEffects
            
            self.color_fx = ColorEffects()
            self.warp_fx = FrameWarpingEffect()
            self.glitch_fx = GlitchEffects()
            self.datamosh_fx = DatamoshEffect()
            self.recursive_fx = RecursiveEffects()
            
            logger.info("✅ All effects initialized successfully")
            
        except ImportError as e:
            logger.error(f"Failed to import effects: {e}")
            raise
    
    def _define_effect_functions(self) -> List[Dict[str, Any]]:
        """Define function schemas for the LLM."""
        return [
            {
                "name": "apply_color_bleed",
                "description": "Apply color channel bleeding for psychedelic effects",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "intensity": {"type": "number", "minimum": 0.1, "maximum": 1.0, "description": "Bleeding intensity"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["intensity"]
                }
            },
            {
                "name": "apply_wave_distortion",
                "description": "Apply wave distortion with enhanced parameters",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "amplitude": {"type": "number", "minimum": 5, "maximum": 100, "description": "Wave amplitude"},
                        "frequency": {"type": "number", "minimum": 0.01, "maximum": 0.5, "description": "Wave frequency"},
                        "direction": {"type": "string", "enum": ["horizontal", "vertical", "both", "radial", "spiral"], "default": "horizontal"},
                        "wave_type": {"type": "string", "enum": ["sine", "cosine", "square", "triangle", "sawtooth"], "default": "sine"},
                        "center_x": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.5},
                        "center_y": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.5},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["amplitude", "frequency"]
                }
            },
            {
                "name": "apply_spiral_distortion",
                "description": "Apply hypnotic spiral distortion",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "intensity": {"type": "number", "minimum": 0.1, "maximum": 2.0, "description": "Spiral intensity"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["intensity"]
                }
            },
            {
                "name": "apply_kaleidoscope",
                "description": "Apply kaleidoscope symmetrical patterns",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "segments": {"type": "integer", "minimum": 3, "maximum": 16, "description": "Number of kaleidoscope segments"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["segments"]
                }
            },
            {
                "name": "apply_digital_noise",
                "description": "Apply digital glitch noise corruption",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "intensity": {"type": "number", "minimum": 0.05, "maximum": 1.0, "description": "Noise intensity"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["intensity"]
                }
            },
            {
                "name": "apply_rgb_shift",
                "description": "Apply RGB channel displacement",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "shift_amount": {"type": "integer", "minimum": 1, "maximum": 30, "description": "Pixel shift amount"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["shift_amount"]
                }
            },
            {
                "name": "apply_chromatic_aberration",
                "description": "Apply lens-like chromatic aberration",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "intensity": {"type": "number", "minimum": 0.1, "maximum": 2.0, "description": "Aberration intensity"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["intensity"]
                }
            },
            {
                "name": "apply_liquid_distortion",
                "description": "Apply liquid-like flowing distortion",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "viscosity": {"type": "number", "minimum": 0.1, "maximum": 1.0, "description": "Liquid viscosity"},
                        "flow_direction": {"type": "number", "minimum": 0, "maximum": 6.28, "description": "Flow direction in radians"},
                        "turbulence": {"type": "number", "minimum": 0.1, "maximum": 1.0, "description": "Turbulence amount"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["viscosity"]
                }
            },
            {
                "name": "apply_vortex_distortion",
                "description": "Apply vortex/whirlpool distortion",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "strength": {"type": "number", "minimum": 0.5, "maximum": 3.0, "description": "Vortex strength"},
                        "center_x": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.5},
                        "center_y": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.5},
                        "radius": {"type": "number", "minimum": 0.2, "maximum": 1.0, "default": 0.5},
                        "clockwise": {"type": "boolean", "default": True},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["strength"]
                }
            },
            {
                "name": "apply_datamosh",
                "description": "Apply motion vector corruption (datamoshing)",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "intensity": {"type": "number", "minimum": 0.1, "maximum": 1.0, "description": "Datamosh intensity"},
                        "target_frames": {"type": "string", "enum": ["all", "first_half", "second_half", "random_subset"], "default": "all"}
                    },
                    "required": ["intensity"]
                }
            }
        ]
    
    def analyze_video_content(self, video_path: Path, max_scenes: int = 3) -> Dict[str, Any]:
        """Analyze video content using scene detection and LLM vision."""
        try:
            from video_analysis.advanced_scene_detection import AdvancedSceneDetector
            
            detector = AdvancedSceneDetector(self.api_key)
            scenes = detector.detect_scenes(video_path)
            
            # Limit to max_scenes for analysis
            if len(scenes) > max_scenes:
                # Select most representative scenes
                scenes = sorted(scenes, key=lambda s: s.confidence, reverse=True)[:max_scenes]
            
            summary = detector.get_scene_summary(scenes)
            
            return {
                'scenes': scenes,
                'summary': summary,
                'total_scenes': len(scenes),
                'analyzed_scenes': min(len(scenes), max_scenes)
            }
            
        except Exception as e:
            logger.error(f"Video analysis failed: {e}")
            return {'scenes': [], 'summary': {}, 'error': str(e)}
    
    def create_content_aware_plan(self, concept: str, style: str, video_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create a film plan based on video content analysis."""
        
        scenes = video_analysis.get('scenes', [])
        scene_descriptions = []
        
        for scene in scenes:
            desc = f"Scene {scene.scene_id}: {scene.content_description} (Duration: {scene.duration:.1f}s, Confidence: {scene.confidence:.2f})"
            scene_descriptions.append(desc)
        
        scene_context = "\n".join(scene_descriptions) if scene_descriptions else "No scene analysis available"
        
        prompt = f"""You are an experimental filmmaker creating a film with the concept "{concept}" in "{style}" style.

AVAILABLE VIDEO CONTENT:
{scene_context}

Create a detailed film plan that uses the actual video content intelligently. Your plan should:
1. Analyze how each scene's content relates to the concept "{concept}"
2. Choose effects that enhance the natural content rather than fighting against it
3. Create a cohesive visual journey that builds on the existing footage

Respond with a JSON object containing an array of segments. Each segment should have:
- segment_id: number
- description: string (what this segment represents conceptually)
- mood: string (emotional tone)
- effects: array of effect function calls
- duration_weight: number (relative duration, 0.5-2.0)

For each effect call, specify:
- function_name: one of the available effect functions
- parameters: object with the effect parameters
- target_frames: "all", "first_half", "second_half", or "random_subset"

IMPORTANT: Choose effects that complement the scene content. For example:
- Use liquid_distortion for water/fluid scenes
- Use vortex_distortion for circular/rotating elements
- Use wave_distortion for organic/natural scenes
- Use digital_noise for urban/technological scenes
- Use kaleidoscope for symmetrical/architectural scenes

Create 3-5 segments that tell a cohesive story."""
        
        try:
            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=[types.Content(role="user", parts=[types.Part.from_text(text=prompt)])],
                config=types.GenerateContentConfig(response_mime_type="application/json")
            )
            
            plan_json = json.loads(response.text)
            
            # Validate and convert to our data structures
            segments = []
            for seg_data in plan_json.get('segments', []):
                effects = []
                for effect_data in seg_data.get('effects', []):
                    effect = EffectCall(
                        function_name=effect_data['function_name'],
                        parameters=effect_data['parameters'],
                        target_frames=effect_data.get('target_frames', 'all'),
                        blend_mode=effect_data.get('blend_mode', 'replace')
                    )
                    effects.append(effect)
                
                segment = FilmSegment(
                    segment_id=seg_data['segment_id'],
                    description=seg_data['description'],
                    mood=seg_data['mood'],
                    effects=effects,
                    duration_weight=seg_data.get('duration_weight', 1.0)
                )
                segments.append(segment)
            
            return {
                'concept': concept,
                'style': style,
                'segments': segments,
                'raw_response': response.text,
                'video_analysis': video_analysis
            }
            
        except Exception as e:
            logger.error(f"Plan creation failed: {e}")
            return {
                'concept': concept,
                'style': style,
                'segments': [],
                'error': str(e)
            }

    def execute_plan(self, plan: Dict[str, Any], frames: List[np.ndarray]) -> List[np.ndarray]:
        """Execute the film plan on the provided frames."""

        segments = plan.get('segments', [])
        if not segments:
            logger.warning("No segments to execute")
            return frames

        logger.info(f"Executing plan with {len(segments)} segments on {len(frames)} frames")

        # Calculate frame distribution based on duration weights
        total_weight = sum(seg.duration_weight for seg in segments)
        frames_per_segment = []

        for segment in segments:
            segment_frames = max(1, int(len(frames) * segment.duration_weight / total_weight))
            frames_per_segment.append(segment_frames)

        # Adjust to ensure we use all frames
        total_assigned = sum(frames_per_segment)
        if total_assigned != len(frames):
            frames_per_segment[-1] += len(frames) - total_assigned

        # Process each segment
        processed_frames = []
        frame_idx = 0

        for i, segment in enumerate(segments):
            segment_frame_count = frames_per_segment[i]
            segment_frames = frames[frame_idx:frame_idx + segment_frame_count]

            logger.info(f"Processing segment {segment.segment_id}: {segment.description}")
            logger.info(f"  Frames: {len(segment_frames)}, Effects: {len(segment.effects)}")

            # Apply effects in sequence
            current_frames = segment_frames.copy()

            for effect in segment.effects:
                try:
                    current_frames = self._apply_effect(effect, current_frames)
                    logger.info(f"  ✅ Applied {effect.function_name}")
                except Exception as e:
                    logger.error(f"  ❌ Failed to apply {effect.function_name}: {e}")

            processed_frames.extend(current_frames)
            frame_idx += segment_frame_count

        return processed_frames

    def _apply_effect(self, effect: EffectCall, frames: List[np.ndarray]) -> List[np.ndarray]:
        """Apply a single effect to frames."""

        # Select target frames
        target_frames = self._select_target_frames(frames, effect.target_frames)

        # Apply the effect (with flexible name matching)
        effect_name = effect.function_name.lower()

        # Map common variations to actual function names
        effect_mapping = {
            'digital_noise': 'apply_digital_noise',
            'wave_distortion': 'apply_wave_distortion',
            'liquid_distortion': 'apply_liquid_distortion',
            'vortex_distortion': 'apply_vortex_distortion',
            'color_shift': 'apply_color_shift',
            'glass_reflection_enhance': 'apply_chromatic_aberration',
            'mirror_effect': 'apply_mirror_effect',
            'flicker': 'apply_digital_noise',
            'fade_to_black': 'apply_digital_noise'
        }

        # Use mapping if available
        if effect_name in effect_mapping:
            effect_name = effect_mapping[effect_name]
        elif not effect_name.startswith('apply_'):
            effect_name = f'apply_{effect_name}'

        if effect_name == "apply_color_bleed":
            intensity = effect.parameters.get('intensity', 0.5)
            for i in target_frames:
                frames[i] = self.color_fx.apply_color_bleed(frames[i], intensity)

        elif effect_name == "apply_wave_distortion":
            amplitude = effect.parameters.get('amplitude', 20)
            frequency = effect.parameters.get('frequency', 0.1)
            direction = effect.parameters.get('direction', 'horizontal')
            wave_type = effect.parameters.get('wave_type', 'sine')
            center_x = effect.parameters.get('center_x', 0.5)
            center_y = effect.parameters.get('center_y', 0.5)

            for i in target_frames:
                frames[i] = self.warp_fx.apply_wave_distortion(
                    frames[i], amplitude, frequency, direction,
                    wave_type=wave_type, center_x=center_x, center_y=center_y
                )

        elif effect_name == "apply_spiral_distortion":
            intensity = effect.parameters.get('intensity', 0.5)
            for i in target_frames:
                frames[i] = self.warp_fx.apply_spiral_distortion(frames[i], intensity)

        elif effect_name == "apply_kaleidoscope":
            segments = effect.parameters.get('segments', 6)
            for i in target_frames:
                frames[i] = self.warp_fx.apply_kaleidoscope(frames[i], segments)

        elif effect_name == "apply_digital_noise":
            intensity = effect.parameters.get('intensity', 0.3)
            for i in target_frames:
                frames[i] = self.glitch_fx.apply_digital_noise(frames[i], intensity)

        elif effect_name == "apply_rgb_shift":
            shift_amount = effect.parameters.get('shift_amount', 5)
            for i in target_frames:
                frames[i] = self.glitch_fx.apply_rgb_shift(frames[i], shift_amount)

        elif effect_name == "apply_chromatic_aberration":
            intensity = effect.parameters.get('intensity', 0.5)
            for i in target_frames:
                frames[i] = self.color_fx.apply_chromatic_aberration(frames[i], intensity)

        elif effect_name == "apply_liquid_distortion":
            viscosity = effect.parameters.get('viscosity', 0.5)
            flow_direction = effect.parameters.get('flow_direction', 0.0)
            turbulence = effect.parameters.get('turbulence', 0.3)
            for i in target_frames:
                frames[i] = self.warp_fx.apply_liquid_distortion(
                    frames[i], viscosity, flow_direction, turbulence
                )

        elif effect_name == "apply_vortex_distortion":
            strength = effect.parameters.get('strength', 1.0)
            center_x = effect.parameters.get('center_x', 0.5)
            center_y = effect.parameters.get('center_y', 0.5)
            radius = effect.parameters.get('radius', 0.5)
            clockwise = effect.parameters.get('clockwise', True)
            for i in target_frames:
                frames[i] = self.warp_fx.apply_vortex_distortion(
                    frames[i], center_x, center_y, strength, radius, clockwise
                )

        elif effect_name == "apply_datamosh":
            intensity = effect.parameters.get('intensity', 0.5)
            # Datamosh works on frame sequences
            if target_frames:
                start_idx = min(target_frames)
                end_idx = max(target_frames) + 1
                sequence = frames[start_idx:end_idx]
                datamoshed = self.datamosh_fx.apply_datamosh(sequence, intensity)
                for j, frame in enumerate(datamoshed):
                    if start_idx + j < len(frames):
                        frames[start_idx + j] = frame

        elif effect_name == "apply_color_shift":
            for i in target_frames:
                frames[i] = self.color_fx.apply_color_shift(frames[i])

        elif effect_name == "apply_mirror_effect":
            direction = effect.parameters.get('direction', 'horizontal')
            for i in target_frames:
                frames[i] = self.warp_fx.apply_mirror_effect(frames[i], direction)

        else:
            logger.warning(f"Unknown effect: {effect.function_name} (mapped to: {effect_name})")

        return frames

    def _select_target_frames(self, frames: List[np.ndarray], target: str) -> List[int]:
        """Select which frames to apply the effect to."""
        total_frames = len(frames)

        if target == "all":
            return list(range(total_frames))
        elif target == "first_half":
            return list(range(total_frames // 2))
        elif target == "second_half":
            return list(range(total_frames // 2, total_frames))
        elif target == "random_subset":
            import random
            subset_size = max(1, total_frames // 3)
            return random.sample(range(total_frames), subset_size)
        else:
            return list(range(total_frames))
