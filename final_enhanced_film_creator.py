"""
Final enhanced film creator with longer duration and more sophisticated effects.
"""

import os
import json
import logging
import cv2
import numpy as np
from pathlib import Path
import base64
from typing import List, Dict, Any
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def analyze_scene_with_gemini(frames_b64: List[str], scene_info: Dict[str, Any]) -> str:
    """Analyze scene content using Gemini vision."""
    try:
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            return "Scene analysis unavailable - no API key"
        
        client = genai.Client(api_key=api_key)
        
        if not frames_b64:
            return "No frames available for analysis"
        
        frame_b64 = frames_b64[0]
        
        prompt = f"""Analyze this video frame from scene {scene_info.get('scene', 'unknown')} 
(duration: {scene_info.get('end', 0) - scene_info.get('start', 0):.1f}s).

Describe:
1. Visual composition and elements
2. Color palette and lighting mood
3. Spatial relationships and depth
4. Potential for visual effects enhancement
5. Emotional or conceptual associations

Keep response under 150 words, focus on elements relevant for experimental video art."""

        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=prompt),
                    types.Part.from_bytes(
                        data=base64.b64decode(frame_b64),
                        mime_type="image/jpeg"
                    )
                ]
            )
        ]
        
        response = client.models.generate_content(
            model="gemini-1.5-flash",
            contents=contents
        )
        
        return response.text.strip()
        
    except Exception as e:
        logger.error(f"Gemini analysis failed: {e}")
        return f"Scene {scene_info.get('scene', 'unknown')} - Analysis failed"


def create_sophisticated_plan(concept: str, style: str, scene_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create sophisticated film plan with detailed effect sequences."""
    try:
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            return {"error": "No API key"}
        
        client = genai.Client(api_key=api_key)
        
        scene_descriptions = []
        for scene in scene_analyses:
            desc = f"Scene {scene['scene']}: {scene['analysis']} (Duration: {scene['duration']:.1f}s)"
            scene_descriptions.append(desc)
        
        scene_context = "\n".join(scene_descriptions)
        
        prompt = f"""Create an experimental film plan for concept "{concept}" in "{style}" style.

SCENE ANALYSIS:
{scene_context}

Create a JSON response with 5-7 segments that build a narrative arc. Each segment should have:
- segment_id: number (1-7)
- title: descriptive title
- description: what this segment represents
- mood: emotional tone
- effects: array of 2-4 effect names
- intensity: "subtle", "moderate", or "intense"
- duration_weight: 0.5-3.0 (relative duration)

Available effects:
color_bleed, wave_distortion, spiral_distortion, kaleidoscope, digital_noise, 
rgb_shift, chromatic_aberration, liquid_distortion, vortex_distortion, datamosh

Build a cohesive journey that:
1. Establishes the concept
2. Develops tension/transformation
3. Reaches a climax
4. Resolves or leaves questions

Focus on how effects enhance the existing visual content."""
        
        response = client.models.generate_content(
            model="gemini-1.5-flash",
            contents=[types.Content(role="user", parts=[types.Part.from_text(text=prompt)])],
            config=types.GenerateContentConfig(response_mime_type="application/json")
        )
        
        return json.loads(response.text)
        
    except Exception as e:
        logger.error(f"Plan creation failed: {e}")
        return {"error": str(e)}


def apply_sophisticated_effects(frames: List[np.ndarray], effects: List[str], intensity: str) -> List[np.ndarray]:
    """Apply effects with intensity control."""
    try:
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        
        color_fx = ColorEffects()
        warp_fx = FrameWarpingEffect()
        glitch_fx = GlitchEffects()
        datamosh_fx = DatamoshEffect()
        
        # Intensity multipliers
        intensity_map = {"subtle": 0.3, "moderate": 0.6, "intense": 1.0}
        mult = intensity_map.get(intensity, 0.6)
        
        processed_frames = frames.copy()
        
        for effect in effects:
            logger.info(f"Applying {effect} with {intensity} intensity")
            
            if effect == "color_bleed":
                processed_frames = [color_fx.apply_color_bleed(f, 0.3 + mult * 0.4) for f in processed_frames]
            elif effect == "wave_distortion":
                amp = int(10 + mult * 30)
                freq = 0.05 + mult * 0.15
                processed_frames = [warp_fx.apply_wave_distortion(f, amplitude=amp, frequency=freq) for f in processed_frames]
            elif effect == "spiral_distortion":
                processed_frames = [warp_fx.apply_spiral_distortion(f, 0.2 + mult * 0.6) for f in processed_frames]
            elif effect == "kaleidoscope":
                segments = int(4 + mult * 8)
                processed_frames = [warp_fx.apply_kaleidoscope(f, segments) for f in processed_frames]
            elif effect == "digital_noise":
                processed_frames = [glitch_fx.apply_digital_noise(f, 0.1 + mult * 0.4) for f in processed_frames]
            elif effect == "rgb_shift":
                shift = int(2 + mult * 15)
                processed_frames = [glitch_fx.apply_rgb_shift(f, shift) for f in processed_frames]
            elif effect == "chromatic_aberration":
                processed_frames = [color_fx.apply_chromatic_aberration(f, 0.2 + mult * 0.8) for f in processed_frames]
            elif effect == "liquid_distortion":
                visc = 0.3 + mult * 0.5
                turb = 0.2 + mult * 0.6
                processed_frames = [warp_fx.apply_liquid_distortion(f, visc, 0.0, turb) for f in processed_frames]
            elif effect == "vortex_distortion":
                strength = 0.5 + mult * 1.5
                processed_frames = [warp_fx.apply_vortex_distortion(f, 0.5, 0.5, strength, 0.6, True) for f in processed_frames]
            elif effect == "datamosh":
                if len(processed_frames) > 1:
                    processed_frames = datamosh_fx.apply_datamosh(processed_frames, 0.2 + mult * 0.6)
        
        return processed_frames
        
    except Exception as e:
        logger.error(f"Error applying effects: {e}")
        return frames


def save_high_quality_video(frames: List[np.ndarray], output_path: Path, fps: int = 30) -> bool:
    """Save high quality video."""
    if not frames:
        return False

    try:
        height, width = frames[0].shape[:2]
        
        # Use high quality codec
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
        
        if not out.isOpened():
            return False
        
        for frame in frames:
            if len(frame.shape) == 3:
                out.write(frame)
            else:
                bgr_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
                out.write(bgr_frame)
        
        out.release()
        
        if output_path.exists() and output_path.stat().st_size > 0:
            logger.info(f"High quality video saved: {output_path} ({output_path.stat().st_size} bytes)")
            return True
        
        return False

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False


def main():
    """Create final enhanced experimental films."""
    logger.info("🎬 FINAL ENHANCED FILM CREATOR")
    
    try:
        from effects.scene_utils import split_scenes
        from video_analysis.frame_extractor import FrameExtractor
        
        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found")
            return False
        
        source_video = video_files[0]
        logger.info(f"Source: {source_video.name}")
        
        # Scene detection with more frames
        logger.info("🔍 Advanced Scene Detection")
        scenes = split_scenes(
            str(source_video),
            use_opencv=True,
            extract_frames=True,
            frames_per_segment=5,  # More frames per scene
            frame_interval=0.5     # Higher frequency
        )
        
        logger.info(f"Detected {len(scenes)} scenes")
        
        # Content analysis
        logger.info("🤖 Deep Content Analysis")
        scene_analyses = []
        
        for scene in scenes[:4]:  # Analyze more scenes
            frames_b64 = scene.get('frames', [])
            if frames_b64:
                analysis = analyze_scene_with_gemini(frames_b64, scene)
                scene_analyses.append({
                    'scene': scene['scene'],
                    'start': scene['start'],
                    'end': scene['end'],
                    'duration': scene['end'] - scene['start'],
                    'analysis': analysis
                })
                logger.info(f"Scene {scene['scene']}: {analysis[:80]}...")
        
        # Extract more frames for longer films
        logger.info("📹 Extracting Extended Frame Sequence")
        extractor = FrameExtractor()
        frames = extractor.extract_frames(source_video, start_time=0, end_time=8)  # 8 seconds
        frame_list = [frame for _, frame in frames[:60]]  # 60 frames for longer films
        
        logger.info(f"Extracted {len(frame_list)} frames")
        
        # Create sophisticated films
        concepts = [
            ("consciousness emergence", "cyberpunk"),
            ("memory dissolution", "surreal"),
            ("reality fragmentation", "glitch")
        ]
        
        output_dir = Path("./output")
        output_dir.mkdir(exist_ok=True)
        
        created_films = []
        
        for concept, style in concepts:
            logger.info(f"\n{'='*70}")
            logger.info(f"🎭 CREATING: '{concept}' ({style})")
            logger.info(f"{'='*70}")
            
            # Create sophisticated plan
            plan = create_sophisticated_plan(concept, style, scene_analyses)
            
            if plan.get('error'):
                logger.error(f"Plan failed: {plan['error']}")
                continue
            
            segments = plan.get('segments', [])
            logger.info(f"Plan: {len(segments)} segments")
            
            # Process with sophisticated effects
            all_frames = []
            total_weight = sum(seg.get('duration_weight', 1.0) for seg in segments)
            
            for segment in segments:
                title = segment.get('title', f"Segment {segment['segment_id']}")
                logger.info(f"Processing: {title}")
                
                # Calculate frames
                weight = segment.get('duration_weight', 1.0)
                segment_frames = max(3, int(len(frame_list) * weight / total_weight))
                
                start_idx = len(all_frames)
                end_idx = min(start_idx + segment_frames, len(frame_list))
                segment_frame_list = frame_list[start_idx:end_idx]
                
                if not segment_frame_list:
                    continue
                
                # Apply sophisticated effects
                effects = segment.get('effects', [])
                intensity = segment.get('intensity', 'moderate')
                
                processed = apply_sophisticated_effects(segment_frame_list, effects, intensity)
                all_frames.extend(processed)
                
                logger.info(f"  {len(processed)} frames, effects: {effects}, intensity: {intensity}")
            
            if not all_frames:
                continue
            
            # Save high quality film
            safe_concept = concept.replace(' ', '_').replace('/', '_')
            output_path = output_dir / f"final_{safe_concept}.mp4"
            
            if save_high_quality_video(all_frames, output_path, fps=30):
                logger.info(f"✅ CREATED: {output_path}")
                created_films.append({
                    'concept': concept,
                    'style': style,
                    'path': str(output_path),
                    'frames': len(all_frames),
                    'segments': len(segments)
                })
                
                # Save plan details
                plan_path = output_dir / f"final_plan_{safe_concept}.json"
                with open(plan_path, 'w') as f:
                    json.dump({
                        'concept': concept,
                        'style': style,
                        'plan': plan,
                        'scene_analyses': scene_analyses,
                        'total_frames': len(all_frames)
                    }, f, indent=2)
            else:
                logger.error(f"❌ FAILED: {output_path}")
        
        # Final summary
        logger.info(f"\n{'='*70}")
        logger.info("🎉 FINAL FILMS COMPLETE!")
        logger.info(f"{'='*70}")
        
        if created_films:
            for film in created_films:
                logger.info(f"📽️  {film['concept']}")
                logger.info(f"    Path: {film['path']}")
                logger.info(f"    Frames: {film['frames']}, Segments: {film['segments']}")
                logger.info(f"    Style: {film['style']}")
            
            logger.info(f"\n🎬 {len(created_films)} sophisticated experimental films created!")
            logger.info("🔍 Scene detection using existing scene_utils.py")
            logger.info("🤖 Content analysis with Gemini API")
            logger.info("📋 Intelligent effect planning")
            logger.info("🎨 Sophisticated effect application")
            logger.info("📹 High quality video output")
            
            return True
        else:
            logger.error("No films created")
            return False
            
    except Exception as e:
        logger.error(f"System failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
