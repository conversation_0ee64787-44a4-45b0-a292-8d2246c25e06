"""
Keyframe-based LLM system for video editing integration.
"""

import os
import json
import logging
import cv2
import numpy as np
from pathlib import Path
import base64
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from dotenv import load_dotenv
from google import genai
from google.genai import types
import math

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class Keyframe:
    """Represents a keyframe with timing and effect parameters."""
    time: float  # Time in seconds
    effect_name: str
    parameters: Dict[str, Any]
    easing: str = "linear"  # "linear", "ease_in", "ease_out", "ease_in_out"
    blend_mode: str = "replace"  # "replace", "overlay", "multiply", "screen"


@dataclass
class EffectTrack:
    """Represents an effect track with multiple keyframes."""
    track_id: int
    effect_type: str
    keyframes: List[Keyframe]
    enabled: bool = True
    opacity: float = 1.0


@dataclass
class Timeline:
    """Represents the complete timeline with multiple tracks."""
    duration: float
    fps: int
    tracks: List[EffectTrack]
    metadata: Dict[str, Any]


class KeyframeSystem:
    """Keyframe-based system for sophisticated video editing."""
    
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY not found in environment")
        
        self.client = genai.Client(api_key=self.api_key)
        
        # Initialize effects
        self._initialize_effects()
    
    def _initialize_effects(self):
        """Initialize all effect classes."""
        try:
            from effects.color_effects import ColorEffects
            from effects.frame_warping import FrameWarpingEffect
            from effects.glitch_effects import GlitchEffects
            from effects.datamosh import DatamoshEffect
            from effects.screen_splitting import ScreenSplittingEffects
            
            self.color_fx = ColorEffects()
            self.warp_fx = FrameWarpingEffect()
            self.glitch_fx = GlitchEffects()
            self.datamosh_fx = DatamoshEffect()
            self.split_fx = ScreenSplittingEffects()
            
            logger.info("✅ All effects initialized for keyframe system")
            
        except ImportError as e:
            logger.error(f"Failed to import effects: {e}")
            raise
    
    def create_intelligent_timeline(self, concept: str, style: str, duration: float, 
                                  scene_analyses: List[Dict[str, Any]]) -> Timeline:
        """Create an intelligent timeline using LLM planning."""
        try:
            # Prepare scene context
            scene_descriptions = []
            for scene in scene_analyses:
                desc = f"Scene {scene.get('scene_id', 'unknown')}: {scene.get('analysis', 'No analysis')[:100]}..."
                scene_descriptions.append(desc)
            
            scene_context = "\n".join(scene_descriptions[:8])  # Limit for prompt
            
            prompt = f"""Create a sophisticated keyframe timeline for a {duration:.1f} second experimental film.

CONCEPT: "{concept}"
STYLE: "{style}"

SCENE ANALYSIS:
{scene_context}

Create a JSON response with a timeline containing 4-6 effect tracks. Each track should have:
- track_id: number (1-6)
- effect_type: one of the available effects
- keyframes: array of keyframes with timing and parameters
- enabled: true
- opacity: 0.3-1.0

Each keyframe should have:
- time: time in seconds (0.0 to {duration:.1f})
- effect_name: specific effect function
- parameters: effect parameters object
- easing: "linear", "ease_in", "ease_out", or "ease_in_out"
- blend_mode: "replace", "overlay", "multiply", or "screen"

Available effects:
- grid_split: Split into grid (rows, cols, border_width)
- irregular_split: Dynamic splits (split_type, num_panels, randomness)
- screen_bleeding: Content spillover (bleed_intensity, bleed_type)
- recursive_splitting: Screens within screens (depth, scale_factor)
- micro_fragmentation: Tiny fragments (fragment_count, min_size, max_size)
- wave_distortion: Flowing effects (amplitude, frequency, direction)
- spiral_distortion: Hypnotic swirls (intensity)
- digital_noise: Glitch corruption (intensity)
- color_bleed: Channel separation (intensity)
- vortex_distortion: Whirlpool effects (strength, center_x, center_y)

Create a timeline that:
1. Starts subtle and builds intensity
2. Uses screen splitting effects for climactic moments
3. Incorporates micro-fragmentation near the peak
4. Has smooth transitions between effects
5. Matches the concept and style

Focus on creating a cinematic progression with proper timing."""
            
            response = self.client.models.generate_content(
                model="gemini-2.5-flash-preview-04-17",
                contents=[types.Content(role="user", parts=[types.Part.from_text(text=prompt)])],
                config=types.GenerateContentConfig(response_mime_type="application/json")
            )
            
            timeline_data = json.loads(response.text)
            
            # Convert to Timeline object
            tracks = []
            for track_data in timeline_data.get('tracks', []):
                keyframes = []
                for kf_data in track_data.get('keyframes', []):
                    keyframe = Keyframe(
                        time=kf_data['time'],
                        effect_name=kf_data['effect_name'],
                        parameters=kf_data['parameters'],
                        easing=kf_data.get('easing', 'linear'),
                        blend_mode=kf_data.get('blend_mode', 'replace')
                    )
                    keyframes.append(keyframe)
                
                track = EffectTrack(
                    track_id=track_data['track_id'],
                    effect_type=track_data['effect_type'],
                    keyframes=keyframes,
                    enabled=track_data.get('enabled', True),
                    opacity=track_data.get('opacity', 1.0)
                )
                tracks.append(track)
            
            timeline = Timeline(
                duration=duration,
                fps=24,
                tracks=tracks,
                metadata={
                    'concept': concept,
                    'style': style,
                    'created_by': 'KeyframeSystem',
                    'scene_count': len(scene_analyses)
                }
            )
            
            return timeline
            
        except Exception as e:
            logger.error(f"Timeline creation failed: {e}")
            # Return basic timeline as fallback
            return self._create_fallback_timeline(duration)
    
    def _create_fallback_timeline(self, duration: float) -> Timeline:
        """Create a basic fallback timeline."""
        tracks = [
            EffectTrack(
                track_id=1,
                effect_type="wave_distortion",
                keyframes=[
                    Keyframe(0.0, "wave_distortion", {"amplitude": 10, "frequency": 0.1}),
                    Keyframe(duration/2, "wave_distortion", {"amplitude": 30, "frequency": 0.2}),
                    Keyframe(duration, "wave_distortion", {"amplitude": 10, "frequency": 0.1})
                ]
            )
        ]
        
        return Timeline(duration=duration, fps=24, tracks=tracks, metadata={})
    
    def interpolate_parameters(self, keyframe1: Keyframe, keyframe2: Keyframe, 
                             current_time: float) -> Dict[str, Any]:
        """Interpolate parameters between two keyframes."""
        if keyframe1.time >= keyframe2.time:
            return keyframe1.parameters
        
        # Calculate interpolation factor
        t = (current_time - keyframe1.time) / (keyframe2.time - keyframe1.time)
        t = max(0.0, min(1.0, t))  # Clamp to [0, 1]
        
        # Apply easing
        t = self._apply_easing(t, keyframe2.easing)
        
        # Interpolate parameters
        result = {}
        for key in keyframe1.parameters:
            if key in keyframe2.parameters:
                val1 = keyframe1.parameters[key]
                val2 = keyframe2.parameters[key]
                
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    # Numeric interpolation
                    result[key] = val1 + (val2 - val1) * t
                else:
                    # Non-numeric: use nearest
                    result[key] = val2 if t > 0.5 else val1
            else:
                result[key] = keyframe1.parameters[key]
        
        # Add any new parameters from keyframe2
        for key in keyframe2.parameters:
            if key not in result:
                result[key] = keyframe2.parameters[key]
        
        return result
    
    def _apply_easing(self, t: float, easing: str) -> float:
        """Apply easing function to interpolation factor."""
        if easing == "ease_in":
            return t * t
        elif easing == "ease_out":
            return 1 - (1 - t) * (1 - t)
        elif easing == "ease_in_out":
            if t < 0.5:
                return 2 * t * t
            else:
                return 1 - 2 * (1 - t) * (1 - t)
        else:  # linear
            return t

    def render_frame_at_time(self, frame: np.ndarray, timeline: Timeline, time: float) -> np.ndarray:
        """Render a frame at a specific time using the timeline."""
        result = frame.copy()

        for track in timeline.tracks:
            if not track.enabled:
                continue

            # Find relevant keyframes for this time
            active_keyframes = [kf for kf in track.keyframes if kf.time <= time]
            future_keyframes = [kf for kf in track.keyframes if kf.time > time]

            if not active_keyframes:
                continue

            # Get current and next keyframe
            current_kf = max(active_keyframes, key=lambda kf: kf.time)
            next_kf = min(future_keyframes, key=lambda kf: kf.time) if future_keyframes else current_kf

            # Interpolate parameters
            if current_kf == next_kf:
                params = current_kf.parameters
            else:
                params = self.interpolate_parameters(current_kf, next_kf, time)

            # Apply effect
            effect_result = self._apply_keyframe_effect(result, current_kf.effect_name, params)

            # Blend with main frame
            result = self._blend_frames(result, effect_result, track.opacity, current_kf.blend_mode)

        return result

    def _apply_keyframe_effect(self, frame: np.ndarray, effect_name: str, parameters: Dict[str, Any]) -> np.ndarray:
        """Apply a specific effect with parameters."""
        try:
            if effect_name == "grid_split":
                rows = int(parameters.get('rows', 2))
                cols = int(parameters.get('cols', 2))
                border_width = int(parameters.get('border_width', 2))
                return self.split_fx.apply_grid_split(frame, rows, cols, border_width)

            elif effect_name == "irregular_split":
                split_type = parameters.get('split_type', 'diagonal')
                num_panels = int(parameters.get('num_panels', 4))
                randomness = parameters.get('randomness', 0.3)
                return self.split_fx.apply_irregular_split(frame, split_type, num_panels, randomness)

            elif effect_name == "recursive_splitting":
                depth = int(parameters.get('depth', 3))
                scale_factor = parameters.get('scale_factor', 0.3)
                return self.split_fx.apply_recursive_splitting(frame, depth, scale_factor)

            elif effect_name == "micro_fragmentation":
                fragment_count = int(parameters.get('fragment_count', 50))
                min_size = int(parameters.get('min_size', 10))
                max_size = int(parameters.get('max_size', 40))
                return self.split_fx.apply_micro_fragmentation(frame, fragment_count, min_size, max_size)

            elif effect_name == "wave_distortion":
                amplitude = parameters.get('amplitude', 20)
                frequency = parameters.get('frequency', 0.1)
                direction = parameters.get('direction', 'horizontal')
                return self.warp_fx.apply_wave_distortion(frame, amplitude, frequency, direction)

            elif effect_name == "spiral_distortion":
                intensity = parameters.get('intensity', 0.5)
                return self.warp_fx.apply_spiral_distortion(frame, intensity)

            elif effect_name == "digital_noise":
                intensity = parameters.get('intensity', 0.3)
                return self.glitch_fx.apply_digital_noise(frame, intensity)

            elif effect_name == "color_bleed":
                intensity = parameters.get('intensity', 0.5)
                return self.color_fx.apply_color_bleed(frame, intensity)

            elif effect_name == "vortex_distortion":
                strength = parameters.get('strength', 1.0)
                center_x = parameters.get('center_x', 0.5)
                center_y = parameters.get('center_y', 0.5)
                radius = parameters.get('radius', 0.5)
                clockwise = parameters.get('clockwise', True)
                return self.warp_fx.apply_vortex_distortion(frame, center_x, center_y, strength, radius, clockwise)

            else:
                logger.warning(f"Unknown effect: {effect_name}")
                return frame

        except Exception as e:
            logger.error(f"Error applying effect {effect_name}: {e}")
            return frame

    def _blend_frames(self, base: np.ndarray, overlay: np.ndarray, opacity: float, blend_mode: str) -> np.ndarray:
        """Blend two frames using specified blend mode."""
        try:
            if blend_mode == "replace":
                return cv2.addWeighted(base, 1-opacity, overlay, opacity, 0)

            elif blend_mode == "overlay":
                # Overlay blend mode
                base_norm = base.astype(np.float32) / 255.0
                overlay_norm = overlay.astype(np.float32) / 255.0

                mask = base_norm < 0.5
                result = np.where(mask,
                                2 * base_norm * overlay_norm,
                                1 - 2 * (1 - base_norm) * (1 - overlay_norm))

                result = (result * 255).astype(np.uint8)
                return cv2.addWeighted(base, 1-opacity, result, opacity, 0)

            elif blend_mode == "multiply":
                result = (base.astype(np.float32) * overlay.astype(np.float32) / 255.0).astype(np.uint8)
                return cv2.addWeighted(base, 1-opacity, result, opacity, 0)

            elif blend_mode == "screen":
                base_inv = 255 - base.astype(np.float32)
                overlay_inv = 255 - overlay.astype(np.float32)
                result = 255 - (base_inv * overlay_inv / 255.0)
                result = result.astype(np.uint8)
                return cv2.addWeighted(base, 1-opacity, result, opacity, 0)

            else:
                return cv2.addWeighted(base, 1-opacity, overlay, opacity, 0)

        except Exception as e:
            logger.error(f"Error blending frames: {e}")
            return base

    def render_timeline(self, frames: List[np.ndarray], timeline: Timeline) -> List[np.ndarray]:
        """Render all frames using the timeline."""
        logger.info(f"Rendering {len(frames)} frames with {len(timeline.tracks)} tracks")

        result_frames = []
        frame_duration = 1.0 / timeline.fps

        for i, frame in enumerate(frames):
            current_time = i * frame_duration

            if current_time > timeline.duration:
                break

            rendered_frame = self.render_frame_at_time(frame, timeline, current_time)
            result_frames.append(rendered_frame)

            if i % 10 == 0:
                logger.info(f"Rendered frame {i}/{len(frames)} (time: {current_time:.2f}s)")

        return result_frames

    def save_timeline(self, timeline: Timeline, path: Path) -> bool:
        """Save timeline to JSON file."""
        try:
            timeline_dict = {
                'duration': timeline.duration,
                'fps': timeline.fps,
                'metadata': timeline.metadata,
                'tracks': [
                    {
                        'track_id': track.track_id,
                        'effect_type': track.effect_type,
                        'enabled': track.enabled,
                        'opacity': track.opacity,
                        'keyframes': [asdict(kf) for kf in track.keyframes]
                    }
                    for track in timeline.tracks
                ]
            }

            with open(path, 'w') as f:
                json.dump(timeline_dict, f, indent=2)

            logger.info(f"Timeline saved: {path}")
            return True

        except Exception as e:
            logger.error(f"Failed to save timeline: {e}")
            return False

    def load_timeline(self, path: Path) -> Optional[Timeline]:
        """Load timeline from JSON file."""
        try:
            with open(path, 'r') as f:
                data = json.load(f)

            tracks = []
            for track_data in data['tracks']:
                keyframes = []
                for kf_data in track_data['keyframes']:
                    keyframe = Keyframe(**kf_data)
                    keyframes.append(keyframe)

                track = EffectTrack(
                    track_id=track_data['track_id'],
                    effect_type=track_data['effect_type'],
                    keyframes=keyframes,
                    enabled=track_data['enabled'],
                    opacity=track_data['opacity']
                )
                tracks.append(track)

            timeline = Timeline(
                duration=data['duration'],
                fps=data['fps'],
                tracks=tracks,
                metadata=data['metadata']
            )

            logger.info(f"Timeline loaded: {path}")
            return timeline

        except Exception as e:
            logger.error(f"Failed to load timeline: {e}")
            return None
