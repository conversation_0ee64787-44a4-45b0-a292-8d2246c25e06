"""
Advanced scene detection using multiple methods and LLM analysis.
"""

import logging
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import base64
from dataclasses import dataclass
from sklearn.cluster import KMeans
from scipy import ndimage
import os
from google import genai
from google.genai import types

logger = logging.getLogger(__name__)


@dataclass
class SceneInfo:
    """Information about a detected scene."""
    scene_id: int
    start_time: float
    end_time: float
    duration: float
    confidence: float
    change_type: str  # 'cut', 'fade', 'dissolve', 'motion'
    content_description: str = ""
    visual_features: Dict[str, Any] = None
    representative_frame: np.ndarray = None


class AdvancedSceneDetector:
    """Advanced scene detection using multiple methods."""
    
    def __init__(self, llm_api_key: str = None):
        self.llm_api_key = llm_api_key or os.getenv("GEMINI_API_KEY")
        if self.llm_api_key:
            self.client = genai.Client(api_key=self.llm_api_key)
        else:
            self.client = None
            logger.warning("No LLM API key provided - content analysis disabled")
    
    def detect_scenes(self, video_path: Path, methods: List[str] = None) -> List[SceneInfo]:
        """
        Detect scenes using multiple methods.
        
        Args:
            video_path: Path to video file
            methods: List of detection methods to use
            
        Returns:
            List of detected scenes
        """
        if methods is None:
            methods = ['histogram', 'optical_flow', 'edge_density', 'color_moments']
        
        logger.info(f"Detecting scenes in {video_path.name} using methods: {methods}")
        
        # Get basic video info
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            logger.error(f"Could not open video: {video_path}")
            return []
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        cap.release()
        
        # Apply each detection method
        all_cuts = []
        
        if 'histogram' in methods:
            cuts = self._detect_histogram_changes(video_path, threshold=0.3)
            all_cuts.extend([(t, 'histogram', conf) for t, conf in cuts])
        
        if 'optical_flow' in methods:
            cuts = self._detect_optical_flow_changes(video_path, threshold=15.0)
            all_cuts.extend([(t, 'optical_flow', conf) for t, conf in cuts])
        
        if 'edge_density' in methods:
            cuts = self._detect_edge_changes(video_path, threshold=0.2)
            all_cuts.extend([(t, 'edge_density', conf) for t, conf in cuts])
        
        if 'color_moments' in methods:
            cuts = self._detect_color_moment_changes(video_path, threshold=0.25)
            all_cuts.extend([(t, 'color_moments', conf) for t, conf in cuts])
        
        # Merge and filter cuts
        scenes = self._merge_scene_cuts(all_cuts, fps, duration)
        
        # Analyze scene content with LLM if available
        if self.client:
            scenes = self._analyze_scene_content(video_path, scenes)
        
        logger.info(f"Detected {len(scenes)} scenes")
        return scenes
    
    def _detect_histogram_changes(self, video_path: Path, threshold: float = 0.3) -> List[Tuple[float, float]]:
        """Detect scene changes using histogram comparison."""
        cap = cv2.VideoCapture(str(video_path))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        cuts = []
        prev_hist = None
        frame_count = 0
        
        # Process every 5th frame for efficiency
        frame_step = 5
        
        while True:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
            ret, frame = cap.read()
            if not ret:
                break
            
            # Calculate histogram
            hist = cv2.calcHist([frame], [0, 1, 2], None, [50, 50, 50], [0, 256, 0, 256, 0, 256])
            hist = cv2.normalize(hist, hist).flatten()
            
            if prev_hist is not None:
                # Compare histograms using correlation
                correlation = cv2.compareHist(prev_hist, hist, cv2.HISTCMP_CORREL)
                
                if correlation < (1 - threshold):
                    timestamp = frame_count / fps
                    confidence = 1 - correlation
                    cuts.append((timestamp, confidence))
            
            prev_hist = hist
            frame_count += frame_step
        
        cap.release()
        return cuts
    
    def _detect_optical_flow_changes(self, video_path: Path, threshold: float = 15.0) -> List[Tuple[float, float]]:
        """Detect scene changes using optical flow analysis."""
        cap = cv2.VideoCapture(str(video_path))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        cuts = []
        prev_gray = None
        frame_count = 0
        frame_step = 3
        
        while True:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
            ret, frame = cap.read()
            if not ret:
                break
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gray = cv2.GaussianBlur(gray, (5, 5), 0)
            
            if prev_gray is not None:
                # Calculate optical flow
                flow = cv2.calcOpticalFlowPyrLK(
                    prev_gray, gray, 
                    np.array([[x, y] for x in range(0, gray.shape[1], 20) 
                             for y in range(0, gray.shape[0], 20)], dtype=np.float32),
                    None
                )[0]
                
                if flow is not None and len(flow) > 0:
                    # Calculate motion magnitude
                    motion_magnitude = np.mean(np.sqrt(flow[:, 0]**2 + flow[:, 1]**2))
                    
                    if motion_magnitude > threshold:
                        timestamp = frame_count / fps
                        confidence = min(motion_magnitude / (threshold * 2), 1.0)
                        cuts.append((timestamp, confidence))
            
            prev_gray = gray
            frame_count += frame_step
        
        cap.release()
        return cuts
    
    def _detect_edge_changes(self, video_path: Path, threshold: float = 0.2) -> List[Tuple[float, float]]:
        """Detect scene changes using edge density analysis."""
        cap = cv2.VideoCapture(str(video_path))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        cuts = []
        prev_edge_density = None
        frame_count = 0
        frame_step = 5
        
        while True:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert to grayscale and detect edges
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            if prev_edge_density is not None:
                density_change = abs(edge_density - prev_edge_density)
                
                if density_change > threshold:
                    timestamp = frame_count / fps
                    confidence = min(density_change / threshold, 1.0)
                    cuts.append((timestamp, confidence))
            
            prev_edge_density = edge_density
            frame_count += frame_step
        
        cap.release()
        return cuts
    
    def _detect_color_moment_changes(self, video_path: Path, threshold: float = 0.25) -> List[Tuple[float, float]]:
        """Detect scene changes using color moment analysis."""
        cap = cv2.VideoCapture(str(video_path))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        cuts = []
        prev_moments = None
        frame_count = 0
        frame_step = 5
        
        while True:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
            ret, frame = cap.read()
            if not ret:
                break
            
            # Calculate color moments (mean, std, skewness)
            moments = []
            for channel in cv2.split(frame):
                mean = np.mean(channel)
                std = np.std(channel)
                skewness = np.mean(((channel - mean) / std) ** 3) if std > 0 else 0
                moments.extend([mean, std, skewness])
            
            moments = np.array(moments)
            
            if prev_moments is not None:
                # Calculate Euclidean distance between moment vectors
                distance = np.linalg.norm(moments - prev_moments) / len(moments)
                
                if distance > threshold:
                    timestamp = frame_count / fps
                    confidence = min(distance / threshold, 1.0)
                    cuts.append((timestamp, confidence))
            
            prev_moments = moments
            frame_count += frame_step
        
        cap.release()
        return cuts
    
    def _merge_scene_cuts(self, all_cuts: List[Tuple[float, str, float]], 
                         fps: float, duration: float) -> List[SceneInfo]:
        """Merge cuts from different methods into coherent scenes."""
        if not all_cuts:
            return [SceneInfo(1, 0.0, duration, duration, 1.0, 'single_scene')]
        
        # Sort cuts by timestamp
        all_cuts.sort(key=lambda x: x[0])
        
        # Merge nearby cuts (within 1 second)
        merged_cuts = []
        for timestamp, method, confidence in all_cuts:
            if not merged_cuts or timestamp - merged_cuts[-1][0] > 1.0:
                merged_cuts.append((timestamp, method, confidence))
            else:
                # Update with higher confidence
                if confidence > merged_cuts[-1][2]:
                    merged_cuts[-1] = (timestamp, method, confidence)
        
        # Create scenes
        scenes = []
        scene_id = 1
        prev_time = 0.0
        
        for timestamp, method, confidence in merged_cuts:
            if timestamp > prev_time + 0.5:  # Minimum scene length
                scene = SceneInfo(
                    scene_id=scene_id,
                    start_time=prev_time,
                    end_time=timestamp,
                    duration=timestamp - prev_time,
                    confidence=confidence,
                    change_type=method
                )
                scenes.append(scene)
                scene_id += 1
                prev_time = timestamp
        
        # Add final scene
        if prev_time < duration - 0.5:
            scene = SceneInfo(
                scene_id=scene_id,
                start_time=prev_time,
                end_time=duration,
                duration=duration - prev_time,
                confidence=1.0,
                change_type='end'
            )
            scenes.append(scene)
        
        return scenes

    def _analyze_scene_content(self, video_path: Path, scenes: List[SceneInfo]) -> List[SceneInfo]:
        """Analyze scene content using LLM vision."""
        if not self.client:
            return scenes

        cap = cv2.VideoCapture(str(video_path))
        fps = cap.get(cv2.CAP_PROP_FPS)

        for scene in scenes:
            try:
                # Extract representative frame from middle of scene
                mid_time = (scene.start_time + scene.end_time) / 2
                frame_number = int(mid_time * fps)

                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                ret, frame = cap.read()

                if ret:
                    # Resize frame for analysis
                    frame_small = cv2.resize(frame, (512, 384))
                    scene.representative_frame = frame_small

                    # Convert to base64 for LLM
                    _, buffer = cv2.imencode('.jpg', frame_small)
                    frame_b64 = base64.b64encode(buffer).decode('utf-8')

                    # Analyze with LLM
                    analysis = self._analyze_frame_with_llm(frame_b64, scene)
                    scene.content_description = analysis.get('description', '')
                    scene.visual_features = analysis.get('features', {})

            except Exception as e:
                logger.warning(f"Failed to analyze scene {scene.scene_id}: {e}")

        cap.release()
        return scenes

    def _analyze_frame_with_llm(self, frame_b64: str, scene: SceneInfo) -> Dict[str, Any]:
        """Analyze a frame using LLM vision."""
        try:
            prompt = f"""Analyze this video frame from scene {scene.scene_id} (duration: {scene.duration:.1f}s).

Provide a concise analysis including:
1. Main visual elements and composition
2. Color palette and lighting
3. Movement or action (if any)
4. Mood or atmosphere
5. Technical aspects (camera angle, depth of field, etc.)

Keep the description under 100 words and focus on visual elements that would be relevant for video editing and effects application."""

            # Create the content with image
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=prompt),
                        types.Part.from_bytes(
                            data=base64.b64decode(frame_b64),
                            mime_type="image/jpeg"
                        )
                    ]
                )
            ]

            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=contents
            )

            description = response.text.strip()

            # Extract basic features from description
            features = {
                'has_motion': any(word in description.lower() for word in ['motion', 'movement', 'moving', 'action']),
                'lighting': 'bright' if any(word in description.lower() for word in ['bright', 'light', 'illuminated']) else 'dark',
                'complexity': 'high' if any(word in description.lower() for word in ['complex', 'detailed', 'busy']) else 'simple'
            }

            return {
                'description': description,
                'features': features
            }

        except Exception as e:
            logger.error(f"LLM analysis failed: {e}")
            return {
                'description': f"Scene {scene.scene_id} - Analysis unavailable",
                'features': {}
            }

    def get_scene_summary(self, scenes: List[SceneInfo]) -> Dict[str, Any]:
        """Get a summary of all detected scenes."""
        if not scenes:
            return {'total_scenes': 0, 'total_duration': 0}

        total_duration = sum(scene.duration for scene in scenes)
        avg_duration = total_duration / len(scenes)

        change_types = {}
        for scene in scenes:
            change_types[scene.change_type] = change_types.get(scene.change_type, 0) + 1

        return {
            'total_scenes': len(scenes),
            'total_duration': total_duration,
            'average_scene_duration': avg_duration,
            'change_type_distribution': change_types,
            'scenes_with_content': sum(1 for s in scenes if s.content_description),
            'high_confidence_scenes': sum(1 for s in scenes if s.confidence > 0.7)
        }
