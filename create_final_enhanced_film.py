"""
Create final experimental film using enhanced effects and scene detection.
"""

import logging
import cv2
import numpy as np
from pathlib import Path
import json
from typing import List
import traceback

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def save_frames_as_video(frames: List[np.ndarray], output_path: Path, fps: int = 24) -> bool:
    """Save frames as video."""
    if not frames:
        return False

    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

        if not out.isOpened():
            return False

        for frame in frames:
            out.write(frame)

        out.release()
        return output_path.exists()

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False


def create_enhanced_experimental_film():
    """Create an experimental film using all enhanced features."""
    logger.info("🎬 Creating Enhanced Experimental Film")
    
    try:
        from gemini_function_planner import GeminiFunctionPlanner
        from video_analysis.frame_extractor import FrameExtractor
        from video_analysis.advanced_scene_detection import AdvancedSceneDetector
        
        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if len(video_files) < 2:
            logger.error("Need at least 2 videos for experimental film")
            return False
        
        logger.info(f"Found {len(video_files)} source videos")
        
        # Initialize components
        planner = GeminiFunctionPlanner()
        extractor = FrameExtractor()
        detector = AdvancedSceneDetector()
        
        # Analyze multiple videos
        all_scenes = []
        all_frames = []
        
        for i, video_file in enumerate(video_files[:3]):  # Use up to 3 videos
            logger.info(f"Analyzing video {i+1}: {video_file.name}")
            
            # Detect scenes
            scenes = detector.detect_scenes(video_file, methods=['histogram', 'optical_flow'])
            all_scenes.extend(scenes)
            
            # Extract frames from best scenes
            best_scenes = sorted(scenes, key=lambda s: s.confidence, reverse=True)[:2]
            
            for scene in best_scenes:
                # Extract frames from this scene
                scene_frames = extractor.extract_frames(
                    video_file, 
                    start_time=scene.start_time, 
                    end_time=min(scene.end_time, scene.start_time + 3.0)  # Max 3 seconds per scene
                )
                
                frame_list = [frame for _, frame in scene_frames]
                all_frames.extend(frame_list)
                
                logger.info(f"  Extracted {len(frame_list)} frames from scene {scene.scene_id}")
        
        if not all_frames:
            logger.error("No frames extracted")
            return False
        
        # Limit total frames for processing
        if len(all_frames) > 60:
            # Select frames evenly distributed
            step = len(all_frames) // 60
            all_frames = all_frames[::step][:60]
        
        logger.info(f"Total frames for processing: {len(all_frames)}")
        
        # Create comprehensive video analysis
        video_analysis = {
            'scenes': all_scenes,
            'summary': {
                'total_scenes': len(all_scenes),
                'total_duration': sum(s.duration for s in all_scenes),
                'high_confidence_scenes': sum(1 for s in all_scenes if s.confidence > 0.7)
            }
        }
        
        # Create multiple experimental films with different concepts
        film_concepts = [
            {
                'name': 'digital_consciousness',
                'concept': 'digital consciousness awakening',
                'style': 'cyberpunk',
                'description': 'Exploration of AI consciousness through digital distortions'
            },
            {
                'name': 'temporal_fragments',
                'concept': 'fragmented memories across time',
                'style': 'nostalgic',
                'description': 'Non-linear narrative through temporal effects'
            },
            {
                'name': 'liquid_reality',
                'concept': 'reality dissolving into liquid dreams',
                'style': 'surreal',
                'description': 'Fluid transformations between real and imagined'
            }
        ]
        
        created_films = []
        
        for film_config in film_concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎭 Creating: {film_config['name']}")
            logger.info(f"Concept: {film_config['concept']}")
            logger.info(f"Style: {film_config['style']}")
            logger.info(f"{'='*60}")
            
            try:
                # Create content-aware plan
                plan = planner.create_content_aware_plan(
                    film_config['concept'], 
                    film_config['style'], 
                    video_analysis
                )
                
                if plan.get('error'):
                    logger.error(f"Plan creation failed: {plan['error']}")
                    continue
                
                # Log plan details
                logger.info(f"Plan created with {len(plan['segments'])} segments:")
                for segment in plan['segments']:
                    logger.info(f"  Segment {segment.segment_id}: {segment.description}")
                    logger.info(f"    Mood: {segment.mood}")
                    logger.info(f"    Effects: {[e.function_name for e in segment.effects]}")
                    logger.info(f"    Duration weight: {segment.duration_weight}")
                
                # Execute plan
                logger.info("Executing plan...")
                processed_frames = planner.execute_plan(plan, all_frames.copy())
                
                # Save film
                output_path = Path(f"./output/enhanced_{film_config['name']}.mp4")
                output_path.parent.mkdir(exist_ok=True)
                
                if save_frames_as_video(processed_frames, output_path, fps=24):
                    logger.info(f"✅ Created film: {output_path}")
                    created_films.append({
                        'name': film_config['name'],
                        'path': str(output_path),
                        'concept': film_config['concept'],
                        'description': film_config['description']
                    })
                else:
                    logger.error(f"❌ Failed to save: {output_path}")
                
                # Save detailed plan
                plan_path = Path(f"./output/plan_{film_config['name']}.json")
                plan_data = {
                    'film_config': film_config,
                    'segments': [
                        {
                            'segment_id': s.segment_id,
                            'description': s.description,
                            'mood': s.mood,
                            'duration_weight': s.duration_weight,
                            'effects': [
                                {
                                    'function_name': e.function_name,
                                    'parameters': e.parameters,
                                    'target_frames': e.target_frames,
                                    'blend_mode': e.blend_mode
                                }
                                for e in s.effects
                            ]
                        }
                        for s in plan['segments']
                    ],
                    'video_analysis': video_analysis['summary'],
                    'source_videos': [str(f) for f in video_files[:3]]
                }
                
                with open(plan_path, 'w') as f:
                    json.dump(plan_data, f, indent=2)
                
                logger.info(f"📋 Plan saved: {plan_path}")
                
            except Exception as e:
                logger.error(f"Failed to create {film_config['name']}: {e}")
                traceback.print_exc()
                continue
        
        # Create summary report
        if created_films:
            summary_path = Path("./output/film_creation_summary.json")
            summary_data = {
                'created_films': created_films,
                'total_films': len(created_films),
                'source_videos_analyzed': len(video_files[:3]),
                'total_scenes_detected': len(all_scenes),
                'total_frames_processed': len(all_frames),
                'scene_detection_methods': ['histogram', 'optical_flow'],
                'effects_used': [
                    'apply_color_bleed', 'apply_wave_distortion', 'apply_spiral_distortion',
                    'apply_kaleidoscope', 'apply_digital_noise', 'apply_rgb_shift',
                    'apply_chromatic_aberration', 'apply_liquid_distortion',
                    'apply_vortex_distortion', 'apply_datamosh'
                ]
            }
            
            with open(summary_path, 'w') as f:
                json.dump(summary_data, f, indent=2)
            
            logger.info(f"\n{'='*60}")
            logger.info("🎉 FILM CREATION COMPLETE!")
            logger.info(f"{'='*60}")
            logger.info(f"Created {len(created_films)} experimental films:")
            
            for film in created_films:
                logger.info(f"  📽️  {film['name']}: {film['path']}")
                logger.info(f"      {film['description']}")
            
            logger.info(f"\n📊 Summary report: {summary_path}")
            
            return True
        else:
            logger.error("❌ No films were created successfully")
            return False
            
    except Exception as e:
        logger.error(f"Film creation failed: {e}")
        traceback.print_exc()
        return False


def create_scene_detection_report():
    """Create a detailed scene detection report."""
    logger.info("📊 Creating Scene Detection Report")
    
    try:
        from video_analysis.advanced_scene_detection import AdvancedSceneDetector
        
        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found")
            return False
        
        detector = AdvancedSceneDetector()
        
        report_data = {
            'detection_methods': ['histogram', 'optical_flow', 'edge_density', 'color_moments'],
            'videos_analyzed': [],
            'overall_statistics': {}
        }
        
        all_scenes = []
        
        for video_file in video_files[:3]:  # Analyze up to 3 videos
            logger.info(f"Analyzing: {video_file.name}")
            
            # Detect scenes with all methods
            scenes = detector.detect_scenes(
                video_file, 
                methods=['histogram', 'optical_flow', 'edge_density', 'color_moments']
            )
            
            summary = detector.get_scene_summary(scenes)
            
            video_data = {
                'filename': video_file.name,
                'scenes_detected': len(scenes),
                'total_duration': summary['total_duration'],
                'average_scene_duration': summary['average_scene_duration'],
                'change_type_distribution': summary['change_type_distribution'],
                'high_confidence_scenes': summary['high_confidence_scenes'],
                'scenes': [
                    {
                        'scene_id': s.scene_id,
                        'start_time': s.start_time,
                        'end_time': s.end_time,
                        'duration': s.duration,
                        'confidence': s.confidence,
                        'change_type': s.change_type,
                        'content_description': s.content_description,
                        'visual_features': s.visual_features
                    }
                    for s in scenes
                ]
            }
            
            report_data['videos_analyzed'].append(video_data)
            all_scenes.extend(scenes)
            
            logger.info(f"  Detected {len(scenes)} scenes")
        
        # Calculate overall statistics
        if all_scenes:
            total_duration = sum(s.duration for s in all_scenes)
            avg_confidence = sum(s.confidence for s in all_scenes) / len(all_scenes)
            
            change_types = {}
            for scene in all_scenes:
                change_types[scene.change_type] = change_types.get(scene.change_type, 0) + 1
            
            report_data['overall_statistics'] = {
                'total_scenes': len(all_scenes),
                'total_duration': total_duration,
                'average_scene_duration': total_duration / len(all_scenes),
                'average_confidence': avg_confidence,
                'change_type_distribution': change_types,
                'scenes_with_content_analysis': sum(1 for s in all_scenes if s.content_description)
            }
        
        # Save report
        report_path = Path("./output/scene_detection_report.json")
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        logger.info(f"✅ Scene detection report saved: {report_path}")
        
        # Print summary
        stats = report_data['overall_statistics']
        logger.info(f"\n📈 SCENE DETECTION SUMMARY:")
        logger.info(f"  Total scenes detected: {stats.get('total_scenes', 0)}")
        logger.info(f"  Average confidence: {stats.get('average_confidence', 0):.2f}")
        logger.info(f"  Average scene duration: {stats.get('average_scene_duration', 0):.1f}s")
        logger.info(f"  Scenes with LLM analysis: {stats.get('scenes_with_content_analysis', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Scene detection report failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Main function to create enhanced experimental films."""
    logger.info("🚀 Starting Enhanced Film Creation System")
    
    # Create output directory
    Path("./output").mkdir(exist_ok=True)
    
    # Step 1: Create scene detection report
    logger.info("\n" + "="*60)
    logger.info("STEP 1: Scene Detection Analysis")
    logger.info("="*60)
    scene_report_success = create_scene_detection_report()
    
    # Step 2: Create experimental films
    logger.info("\n" + "="*60)
    logger.info("STEP 2: Enhanced Film Creation")
    logger.info("="*60)
    film_creation_success = create_enhanced_experimental_film()
    
    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info("🎯 FINAL RESULTS")
    logger.info(f"{'='*60}")
    
    logger.info(f"Scene Detection Report: {'✅ SUCCESS' if scene_report_success else '❌ FAILED'}")
    logger.info(f"Film Creation: {'✅ SUCCESS' if film_creation_success else '❌ FAILED'}")
    
    if film_creation_success:
        logger.info("\n🎬 Your experimental films are ready!")
        logger.info("Check the ./output/ directory for:")
        logger.info("  📽️  Enhanced experimental films (.mp4)")
        logger.info("  📋 Detailed creation plans (.json)")
        logger.info("  📊 Scene detection report")
        logger.info("  📈 Film creation summary")
    
    return scene_report_success and film_creation_success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
