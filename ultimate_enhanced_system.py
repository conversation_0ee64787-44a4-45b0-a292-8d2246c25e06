"""
Ultimate enhanced film system with screen splitting, keyframes, and latest Gemini model.
"""

import os
import json
import logging
import cv2
import numpy as np
from pathlib import Path
import base64
from typing import List, Dict, Any
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UltimateEnhancedSystem:
    """Ultimate film creation system with all advanced features."""
    
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY not found in environment")
        
        self.client = genai.Client(api_key=self.api_key)
        
        # Initialize all systems
        self._initialize_systems()
    
    def _initialize_systems(self):
        """Initialize all subsystems."""
        try:
            from comprehensive_film_system import ComprehensiveFilmSystem
            from keyframe_system import KeyframeSystem
            from effects.screen_splitting import ScreenSplittingEffects
            
            self.film_system = ComprehensiveFilmSystem()
            self.keyframe_system = KeyframeSystem()
            self.split_fx = ScreenSplittingEffects()
            
            logger.info("✅ All systems initialized")
            
        except ImportError as e:
            logger.error(f"Failed to import systems: {e}")
            raise
    
    def create_ultimate_film(self, concept: str, style: str, target_duration: float = 45.0) -> bool:
        """Create the ultimate experimental film with all features."""
        logger.info(f"🚀 ULTIMATE ENHANCED SYSTEM - Creating '{concept}' ({style})")
        
        try:
            # Step 1: Multi-video scene detection with PySceneDetect
            logger.info("\n" + "="*70)
            logger.info("STEP 1: Advanced Scene Detection (PySceneDetect)")
            logger.info("="*70)
            
            video_dir = Path("C:/Quick Share")
            all_video_scenes = self.film_system.detect_scenes_all_videos(video_dir)
            
            if not all_video_scenes:
                logger.error("No scenes detected")
                return False
            
            # Step 2: Comprehensive scene analysis with latest Gemini
            logger.info("\n" + "="*70)
            logger.info("STEP 2: Deep Content Analysis (Gemini 2.5 Flash)")
            logger.info("="*70)
            
            analysis_results = self._analyze_with_latest_gemini(all_video_scenes)
            
            if analysis_results['analyzed_scenes'] == 0:
                logger.error("No scenes analyzed")
                return False
            
            # Step 3: Extract frames for processing
            logger.info("\n" + "="*70)
            logger.info("STEP 3: Frame Extraction for Ultimate Film")
            logger.info("="*70)
            
            target_frames = int(target_duration * 24)  # 24fps
            all_frames = self.film_system.extract_frames_for_film(analysis_results, target_frames)
            
            if not all_frames:
                logger.error("No frames extracted")
                return False
            
            # Step 4: Create intelligent keyframe timeline
            logger.info("\n" + "="*70)
            logger.info("STEP 4: Intelligent Keyframe Timeline Creation")
            logger.info("="*70)
            
            scene_analyses = []
            for video_data in analysis_results['videos'].values():
                scene_analyses.extend(video_data['scenes'])
            
            timeline = self.keyframe_system.create_intelligent_timeline(
                concept, style, target_duration, scene_analyses
            )
            
            logger.info(f"Timeline created: {len(timeline.tracks)} tracks, {target_duration}s duration")
            
            # Step 5: Apply advanced screen splitting effects
            logger.info("\n" + "="*70)
            logger.info("STEP 5: Advanced Screen Splitting & Effects")
            logger.info("="*70)
            
            enhanced_frames = self._apply_advanced_screen_effects(all_frames, timeline, concept)
            
            # Step 6: Render with keyframe system
            logger.info("\n" + "="*70)
            logger.info("STEP 6: Keyframe-Based Rendering")
            logger.info("="*70)
            
            final_frames = self.keyframe_system.render_timeline(enhanced_frames, timeline)
            
            # Step 7: Save ultimate film
            logger.info("\n" + "="*70)
            logger.info("STEP 7: Ultimate Film Creation")
            logger.info("="*70)
            
            output_dir = Path("./output")
            output_dir.mkdir(exist_ok=True)
            
            safe_concept = concept.replace(' ', '_').replace('/', '_')
            output_path = output_dir / f"ultimate_{safe_concept}.mp4"
            
            success = self._save_ultimate_video(final_frames, output_path, fps=24)
            
            if success:
                # Save timeline and metadata
                timeline_path = output_dir / f"ultimate_timeline_{safe_concept}.json"
                self.keyframe_system.save_timeline(timeline, timeline_path)
                
                # Save comprehensive metadata
                metadata_path = output_dir / f"ultimate_metadata_{safe_concept}.json"
                self._save_metadata(metadata_path, concept, style, analysis_results, timeline)
                
                logger.info(f"\n{'='*70}")
                logger.info("🎉 ULTIMATE FILM COMPLETE!")
                logger.info(f"{'='*70}")
                logger.info(f"📽️  Film: {output_path}")
                logger.info(f"📋 Timeline: {timeline_path}")
                logger.info(f"📊 Metadata: {metadata_path}")
                logger.info(f"⏱️  Duration: {target_duration}s")
                logger.info(f"🎬 Frames: {len(final_frames)}")
                logger.info(f"🎭 Tracks: {len(timeline.tracks)}")
                
                return True
            else:
                logger.error("Failed to save ultimate film")
                return False
                
        except Exception as e:
            logger.error(f"Ultimate system failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _analyze_with_latest_gemini(self, all_video_scenes: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Analyze scenes with latest Gemini model."""
        logger.info("🤖 Using Gemini 2.5 Flash Preview for analysis")
        
        analysis_results = {
            'timestamp': '2024-12-19',
            'model': 'gemini-2.5-flash-preview-04-17',
            'videos': {},
            'total_scenes': 0,
            'analyzed_scenes': 0
        }
        
        for video_path, scenes in all_video_scenes.items():
            video_name = Path(video_path).name
            logger.info(f"Analyzing {video_name} with latest Gemini")
            
            video_analysis = {
                'video_path': video_path,
                'scenes': [],
                'total_duration': 0
            }
            
            for scene in scenes:
                frames_b64 = scene.get('frames', [])
                
                if frames_b64:
                    try:
                        analysis = self._analyze_scene_with_latest_gemini(frames_b64, scene, video_name)
                        
                        scene_data = {
                            'scene_id': scene['scene'],
                            'start_time': scene['start'],
                            'end_time': scene['end'],
                            'duration': scene['end'] - scene['start'],
                            'analysis': analysis,
                            'frame_count': len(frames_b64)
                        }
                        
                        video_analysis['scenes'].append(scene_data)
                        video_analysis['total_duration'] += scene_data['duration']
                        analysis_results['analyzed_scenes'] += 1
                        
                        logger.info(f"  Scene {scene['scene']}: {analysis[:80]}...")
                        
                    except Exception as e:
                        logger.error(f"  Failed to analyze scene {scene['scene']}: {e}")
                        continue
                
                analysis_results['total_scenes'] += 1
            
            analysis_results['videos'][video_name] = video_analysis
        
        # Save analysis
        analysis_path = Path("./output/ultimate_scene_analysis.json")
        analysis_path.parent.mkdir(exist_ok=True)
        
        with open(analysis_path, 'w') as f:
            json.dump(analysis_results, f, indent=2)
        
        logger.info(f"📊 Ultimate analysis saved: {analysis_path}")
        return analysis_results
    
    def _analyze_scene_with_latest_gemini(self, frames_b64: List[str], scene_info: Dict[str, Any], video_name: str) -> str:
        """Analyze scene with latest Gemini model."""
        try:
            if not frames_b64:
                return "No frames available"
            
            frame_b64 = frames_b64[0]
            
            prompt = f"""Analyze this video frame from {video_name}, scene {scene_info.get('scene', 'unknown')} 
for advanced experimental video art creation.

Provide detailed analysis for:
1. Visual composition and geometric elements
2. Color theory and lighting dynamics
3. Spatial relationships and depth perception
4. Motion potential and kinetic energy
5. Emotional resonance and psychological impact
6. Optimal effects for enhancement (screen splitting, distortion, fragmentation)
7. Narrative or conceptual associations

Focus on elements that would benefit from:
- Grid splitting (browser tabs/app-like layouts)
- Irregular splits (diagonal tears, circular viewports)
- Screen bleeding effects
- Recursive splitting (mirrors within mirrors)
- Micro-fragmentation for climactic moments

Keep response under 250 words but be specific about visual enhancement opportunities."""

            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=prompt),
                        types.Part.from_bytes(
                            data=base64.b64decode(frame_b64),
                            mime_type="image/jpeg"
                        )
                    ]
                )
            ]
            
            response = self.client.models.generate_content(
                model="gemini-2.5-flash-preview-04-17",
                contents=contents
            )
            
            return response.text.strip()
            
        except Exception as e:
            logger.error(f"Latest Gemini analysis failed: {e}")
            return f"Scene {scene_info.get('scene', 'unknown')} - Analysis failed"

    def _apply_advanced_screen_effects(self, frames: List[np.ndarray], timeline, concept: str) -> List[np.ndarray]:
        """Apply advanced screen splitting effects based on concept."""
        logger.info(f"🎨 Applying advanced screen effects for '{concept}'")

        enhanced_frames = []
        total_frames = len(frames)

        for i, frame in enumerate(frames):
            progress = i / total_frames

            # Progressive complexity based on concept and timeline position
            if concept.lower().find('consciousness') != -1:
                # Consciousness theme: Start simple, build to complex recursive splitting
                if progress < 0.2:
                    # Simple grid split (2x2 like browser tabs)
                    result = self.split_fx.apply_grid_split(frame, rows=2, cols=2, border_width=3)
                elif progress < 0.4:
                    # Split each half again (4 screens)
                    result = self.split_fx.apply_grid_split(frame, rows=2, cols=4, border_width=2)
                elif progress < 0.6:
                    # Split again (8 screens)
                    result = self.split_fx.apply_grid_split(frame, rows=2, cols=8, border_width=1)
                elif progress < 0.8:
                    # Irregular dynamic splits
                    result = self.split_fx.apply_irregular_split(frame, "diagonal", 6, 0.4)
                else:
                    # Recursive splitting climax
                    result = self.split_fx.apply_recursive_splitting(frame, depth=4, scale_factor=0.25)

            elif concept.lower().find('fragmentation') != -1:
                # Fragmentation theme: Build to micro-fragmentation climax
                if progress < 0.3:
                    # Circular viewports
                    result = self.split_fx.apply_irregular_split(frame, "circular", 4, 0.3)
                elif progress < 0.6:
                    # Spiral inward splits
                    result = self.split_fx.apply_irregular_split(frame, "spiral", 8, 0.5)
                elif progress < 0.8:
                    # Organic flowing splits
                    result = self.split_fx.apply_irregular_split(frame, "organic", 12, 0.7)
                else:
                    # Micro-fragmentation climax (dozens of tiny screens)
                    fragment_count = int(30 + progress * 70)  # 30-100 fragments
                    result = self.split_fx.apply_micro_fragmentation(frame, fragment_count, 8, 25)

            elif concept.lower().find('reality') != -1:
                # Reality theme: Diagonal tears and bleeding
                if progress < 0.25:
                    # Start with simple diagonal tears
                    result = self.split_fx.apply_irregular_split(frame, "diagonal", 3, 0.2)
                elif progress < 0.5:
                    # More complex tears
                    result = self.split_fx.apply_irregular_split(frame, "diagonal", 6, 0.5)
                elif progress < 0.75:
                    # Add recursive elements
                    result = self.split_fx.apply_recursive_splitting(frame, depth=3, scale_factor=0.3)
                else:
                    # Final fragmentation
                    result = self.split_fx.apply_micro_fragmentation(frame, 60, 5, 30)

            else:
                # Default progression
                if progress < 0.33:
                    result = self.split_fx.apply_grid_split(frame, rows=2, cols=2)
                elif progress < 0.66:
                    result = self.split_fx.apply_irregular_split(frame, "circular", 5, 0.4)
                else:
                    result = self.split_fx.apply_recursive_splitting(frame, depth=3, scale_factor=0.3)

            enhanced_frames.append(result)

            if i % 20 == 0:
                logger.info(f"  Enhanced frame {i}/{total_frames} (progress: {progress:.1%})")

        # Apply screen bleeding effects to sequences
        logger.info("🩸 Applying screen bleeding effects")

        # Apply bleeding to segments
        segment_size = len(enhanced_frames) // 4

        for start in range(0, len(enhanced_frames), segment_size):
            end = min(start + segment_size, len(enhanced_frames))
            segment = enhanced_frames[start:end]

            if len(segment) > 1:
                # Different bleeding types for different segments
                if start == 0:
                    bled_segment = self.split_fx.apply_screen_bleeding(segment, 0.2, "edge")
                elif start == segment_size:
                    bled_segment = self.split_fx.apply_screen_bleeding(segment, 0.4, "flash")
                else:
                    bled_segment = self.split_fx.apply_screen_bleeding(segment, 0.3, "overflow")

                enhanced_frames[start:end] = bled_segment

        logger.info(f"✅ Advanced screen effects applied to {len(enhanced_frames)} frames")
        return enhanced_frames

    def _save_ultimate_video(self, frames: List[np.ndarray], output_path: Path, fps: int = 24) -> bool:
        """Save ultimate quality video."""
        if not frames:
            return False

        try:
            height, width = frames[0].shape[:2]

            # Use high quality codec
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

            if not out.isOpened():
                logger.error("Failed to open video writer")
                return False

            logger.info(f"Saving {len(frames)} frames to {output_path}")

            for i, frame in enumerate(frames):
                if len(frame.shape) == 3:
                    out.write(frame)
                else:
                    bgr_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
                    out.write(bgr_frame)

                if i % 50 == 0:
                    logger.info(f"  Saved frame {i}/{len(frames)}")

            out.release()

            if output_path.exists() and output_path.stat().st_size > 0:
                duration = len(frames) / fps
                size_mb = output_path.stat().st_size / (1024 * 1024)
                logger.info(f"✅ Ultimate video saved: {output_path} ({size_mb:.1f}MB, {duration:.1f}s)")
                return True

            return False

        except Exception as e:
            logger.error(f"Error saving ultimate video: {e}")
            return False

    def _save_metadata(self, path: Path, concept: str, style: str, analysis_results: Dict[str, Any], timeline) -> bool:
        """Save comprehensive metadata."""
        try:
            metadata = {
                'concept': concept,
                'style': style,
                'creation_timestamp': '2024-12-19',
                'system_version': 'Ultimate Enhanced v1.0',
                'gemini_model': 'gemini-2.5-flash-preview-04-17',
                'features_used': [
                    'PySceneDetect multi-video analysis',
                    'Gemini 2.5 Flash content analysis',
                    'Advanced screen splitting effects',
                    'Keyframe-based timeline system',
                    'Screen bleeding effects',
                    'Recursive splitting',
                    'Micro-fragmentation',
                    'Intelligent effect progression'
                ],
                'video_analysis_summary': {
                    'total_videos': len(analysis_results['videos']),
                    'total_scenes': analysis_results['total_scenes'],
                    'analyzed_scenes': analysis_results['analyzed_scenes']
                },
                'timeline_summary': {
                    'duration': timeline.duration,
                    'fps': timeline.fps,
                    'tracks': len(timeline.tracks),
                    'total_keyframes': sum(len(track.keyframes) for track in timeline.tracks)
                },
                'effects_applied': [
                    'grid_split (2x2, 2x4, 2x8 progression)',
                    'irregular_split (diagonal, circular, spiral, organic)',
                    'recursive_splitting (screens within screens)',
                    'micro_fragmentation (up to 100 tiny screens)',
                    'screen_bleeding (edge, flash, overflow)',
                    'keyframe interpolation with easing',
                    'multi-track blending'
                ]
            }

            with open(path, 'w') as f:
                json.dump(metadata, f, indent=2)

            logger.info(f"📊 Ultimate metadata saved: {path}")
            return True

        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
            return False


def main():
    """Main function to create ultimate enhanced film."""
    logger.info("🚀 ULTIMATE ENHANCED FILM SYSTEM")

    try:
        system = UltimateEnhancedSystem()

        # Create multiple ultimate films
        concepts = [
            ("digital consciousness emergence", "cyberpunk"),
            ("reality fragmentation matrix", "glitch"),
            ("memory dissolution cascade", "surreal")
        ]

        created_films = []

        for concept, style in concepts:
            logger.info(f"\n{'='*80}")
            logger.info(f"🎭 CREATING ULTIMATE FILM: '{concept}' ({style})")
            logger.info(f"{'='*80}")

            success = system.create_ultimate_film(concept, style, target_duration=45.0)

            if success:
                created_films.append((concept, style))
                logger.info(f"✅ Ultimate film created: {concept}")
            else:
                logger.error(f"❌ Failed to create: {concept}")

        # Final summary
        logger.info(f"\n{'='*80}")
        logger.info("🎉 ULTIMATE SYSTEM COMPLETE!")
        logger.info(f"{'='*80}")

        if created_films:
            logger.info(f"Created {len(created_films)} ultimate films:")
            for concept, style in created_films:
                logger.info(f"  📽️  {concept} ({style})")

            logger.info(f"\n🎬 ULTIMATE FEATURES:")
            logger.info(f"🔍 PySceneDetect for all videos")
            logger.info(f"🤖 Gemini 2.5 Flash Preview analysis")
            logger.info(f"📱 Advanced screen splitting (2x2 → 2x8 → micro-fragments)")
            logger.info(f"🩸 Screen bleeding effects")
            logger.info(f"🔄 Recursive splitting (mirrors within mirrors)")
            logger.info(f"⚡ Keyframe-based timeline system")
            logger.info(f"🎨 Intelligent effect progression")
            logger.info(f"⏱️  45+ second duration")
            logger.info(f"📹 Ultra high quality output")

            return True
        else:
            logger.error("No ultimate films were created")
            return False

    except Exception as e:
        logger.error(f"Ultimate system failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
