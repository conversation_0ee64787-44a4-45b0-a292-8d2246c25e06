"""
Comprehensive film system using PySceneDetect for all videos with 30+ second films.
"""

import os
import json
import logging
import cv2
import numpy as np
from pathlib import Path
import base64
from typing import List, Dict, Any
from dotenv import load_dotenv
from google import genai
from google.genai import types
import pickle
from datetime import datetime

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ComprehensiveFilmSystem:
    """Complete film creation system with multi-video processing."""
    
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY not found in environment")
        
        self.client = genai.Client(api_key=self.api_key)
        self.video_analyses = {}
        self.all_scenes = []
        
    def detect_scenes_all_videos(self, video_dir: Path) -> Dict[str, List[Dict[str, Any]]]:
        """Detect scenes in all videos using PySceneDetect."""
        logger.info("🔍 Detecting scenes in all videos using PySceneDetect")
        
        from effects.scene_utils import split_scenes
        
        video_files = list(video_dir.glob("*.mp4"))
        if not video_files:
            logger.error("No video files found")
            return {}
        
        all_video_scenes = {}
        
        for video_file in video_files:
            logger.info(f"Processing video: {video_file.name}")
            
            try:
                # Use PySceneDetect with frame extraction
                scenes = split_scenes(
                    str(video_file),
                    bypass_scene_detection=False,  # Use scene detection
                    extract_frames=True,
                    frames_per_segment=5,
                    frame_interval=1.0,
                    use_opencv=False,  # Don't use OpenCV
                    force_pyscenedetect=True  # Force PySceneDetect
                )
                
                if scenes:
                    all_video_scenes[str(video_file)] = scenes
                    logger.info(f"  Detected {len(scenes)} scenes in {video_file.name}")
                else:
                    logger.warning(f"  No scenes detected in {video_file.name}")
                    
            except Exception as e:
                logger.error(f"  Failed to process {video_file.name}: {e}")
                continue
        
        logger.info(f"Total videos processed: {len(all_video_scenes)}")
        return all_video_scenes
    
    def analyze_scenes_with_gemini(self, all_video_scenes: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Analyze all scenes with Gemini and store results."""
        logger.info("🤖 Analyzing all scenes with Gemini")
        
        analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'videos': {},
            'total_scenes': 0,
            'analyzed_scenes': 0
        }
        
        for video_path, scenes in all_video_scenes.items():
            video_name = Path(video_path).name
            logger.info(f"Analyzing scenes from {video_name}")
            
            video_analysis = {
                'video_path': video_path,
                'scenes': [],
                'total_duration': 0
            }
            
            for scene in scenes:
                frames_b64 = scene.get('frames', [])
                
                if frames_b64:
                    try:
                        analysis = self._analyze_scene_with_gemini(frames_b64, scene, video_name)
                        
                        scene_data = {
                            'scene_id': scene['scene'],
                            'start_time': scene['start'],
                            'end_time': scene['end'],
                            'duration': scene['end'] - scene['start'],
                            'analysis': analysis,
                            'frame_count': len(frames_b64)
                        }
                        
                        video_analysis['scenes'].append(scene_data)
                        video_analysis['total_duration'] += scene_data['duration']
                        analysis_results['analyzed_scenes'] += 1
                        
                        logger.info(f"  Scene {scene['scene']}: {analysis[:60]}...")
                        
                    except Exception as e:
                        logger.error(f"  Failed to analyze scene {scene['scene']}: {e}")
                        continue
                
                analysis_results['total_scenes'] += 1
            
            analysis_results['videos'][video_name] = video_analysis
            logger.info(f"  Completed {video_name}: {len(video_analysis['scenes'])} scenes analyzed")
        
        # Save analysis results
        analysis_path = Path("./output/comprehensive_scene_analysis.json")
        analysis_path.parent.mkdir(exist_ok=True)
        
        with open(analysis_path, 'w') as f:
            json.dump(analysis_results, f, indent=2)
        
        logger.info(f"📊 Scene analysis saved: {analysis_path}")
        logger.info(f"Total scenes: {analysis_results['total_scenes']}, Analyzed: {analysis_results['analyzed_scenes']}")
        
        return analysis_results
    
    def _analyze_scene_with_gemini(self, frames_b64: List[str], scene_info: Dict[str, Any], video_name: str) -> str:
        """Analyze individual scene with Gemini."""
        try:
            if not frames_b64:
                return "No frames available for analysis"
            
            frame_b64 = frames_b64[0]  # Use first frame
            
            prompt = f"""Analyze this video frame from {video_name}, scene {scene_info.get('scene', 'unknown')} 
(duration: {scene_info.get('end', 0) - scene_info.get('start', 0):.1f}s).

Provide analysis for experimental video art:
1. Visual composition and key elements
2. Color palette and lighting characteristics  
3. Spatial depth and perspective
4. Movement potential and dynamic elements
5. Emotional tone and conceptual associations
6. Suitability for specific visual effects

Keep response under 200 words, focus on elements relevant for creative video manipulation."""

            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=prompt),
                        types.Part.from_bytes(
                            data=base64.b64decode(frame_b64),
                            mime_type="image/jpeg"
                        )
                    ]
                )
            ]
            
            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=contents
            )
            
            return response.text.strip()
            
        except Exception as e:
            logger.error(f"Gemini analysis failed: {e}")
            return f"Scene {scene_info.get('scene', 'unknown')} - Analysis failed"
    
    def create_film_plan(self, concept: str, style: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive film plan using all analyzed scenes."""
        try:
            # Collect all scene analyses
            all_scene_descriptions = []
            
            for video_name, video_data in analysis_results['videos'].items():
                for scene in video_data['scenes']:
                    desc = f"{video_name} Scene {scene['scene_id']}: {scene['analysis'][:100]}... (Duration: {scene['duration']:.1f}s)"
                    all_scene_descriptions.append(desc)
            
            scene_context = "\n".join(all_scene_descriptions[:10])  # Limit to first 10 for prompt
            
            prompt = f"""Create an experimental film plan for concept "{concept}" in "{style}" style.

AVAILABLE SCENES FROM MULTIPLE VIDEOS:
{scene_context}

Create a JSON response with 8-12 segments for a 30+ second film. Each segment should have:
- segment_id: number (1-12)
- title: descriptive title
- description: what this segment represents conceptually
- mood: emotional tone
- effects: array of 2-4 effect names
- intensity: "subtle", "moderate", or "intense"
- duration_weight: 0.5-4.0 (higher = longer duration)

Available effects:
color_bleed, wave_distortion, spiral_distortion, kaleidoscope, digital_noise, 
rgb_shift, chromatic_aberration, liquid_distortion, vortex_distortion, datamosh

Build a narrative arc with:
1. Introduction (2-3 segments)
2. Development (3-4 segments) 
3. Climax (2-3 segments)
4. Resolution (2-3 segments)

Focus on creating a cohesive 30+ second experimental film that tells a story through visual transformation."""
            
            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=[types.Content(role="user", parts=[types.Part.from_text(text=prompt)])],
                config=types.GenerateContentConfig(response_mime_type="application/json")
            )
            
            plan = json.loads(response.text)
            
            # Calculate estimated duration
            total_weight = sum(seg.get('duration_weight', 1.0) for seg in plan.get('segments', []))
            estimated_duration = total_weight * 3  # Rough estimate: 3 seconds per weight unit
            
            plan['estimated_duration'] = estimated_duration
            plan['concept'] = concept
            plan['style'] = style
            
            return plan
            
        except Exception as e:
            logger.error(f"Plan creation failed: {e}")
            return {"error": str(e)}
    
    def extract_frames_for_film(self, analysis_results: Dict[str, Any], target_frames: int = 240) -> List[np.ndarray]:
        """Extract frames from all videos for film creation."""
        logger.info(f"📹 Extracting {target_frames} frames from all videos")
        
        from video_analysis.frame_extractor import FrameExtractor
        extractor = FrameExtractor()
        
        all_frames = []
        
        for video_name, video_data in analysis_results['videos'].items():
            video_path = Path(video_data['video_path'])
            
            if not video_path.exists():
                logger.warning(f"Video not found: {video_path}")
                continue
            
            # Extract frames from this video
            try:
                # Extract more frames for longer duration
                frames = extractor.extract_frames(video_path, start_time=0, end_time=15)  # 15 seconds
                frame_list = [frame for _, frame in frames]
                
                all_frames.extend(frame_list)
                logger.info(f"  Extracted {len(frame_list)} frames from {video_name}")
                
                # Stop if we have enough frames
                if len(all_frames) >= target_frames:
                    break
                    
            except Exception as e:
                logger.error(f"  Failed to extract frames from {video_name}: {e}")
                continue
        
        # Trim to target number
        if len(all_frames) > target_frames:
            # Select evenly distributed frames
            step = len(all_frames) // target_frames
            all_frames = all_frames[::step][:target_frames]
        
        logger.info(f"Total frames for film creation: {len(all_frames)}")
        return all_frames

    def apply_sophisticated_effects(self, frames: List[np.ndarray], effects: List[str], intensity: str) -> List[np.ndarray]:
        """Apply effects with intensity control."""
        try:
            from effects.color_effects import ColorEffects
            from effects.frame_warping import FrameWarpingEffect
            from effects.glitch_effects import GlitchEffects
            from effects.datamosh import DatamoshEffect

            color_fx = ColorEffects()
            warp_fx = FrameWarpingEffect()
            glitch_fx = GlitchEffects()
            datamosh_fx = DatamoshEffect()

            # Intensity multipliers
            intensity_map = {"subtle": 0.3, "moderate": 0.6, "intense": 1.0}
            mult = intensity_map.get(intensity, 0.6)

            processed_frames = frames.copy()

            for effect in effects:
                logger.info(f"  Applying {effect} with {intensity} intensity")

                if effect == "color_bleed":
                    processed_frames = [color_fx.apply_color_bleed(f, 0.2 + mult * 0.5) for f in processed_frames]
                elif effect == "wave_distortion":
                    amp = int(8 + mult * 25)
                    freq = 0.03 + mult * 0.12
                    processed_frames = [warp_fx.apply_wave_distortion(f, amplitude=amp, frequency=freq) for f in processed_frames]
                elif effect == "spiral_distortion":
                    processed_frames = [warp_fx.apply_spiral_distortion(f, 0.1 + mult * 0.7) for f in processed_frames]
                elif effect == "kaleidoscope":
                    segments = int(3 + mult * 9)
                    processed_frames = [warp_fx.apply_kaleidoscope(f, segments) for f in processed_frames]
                elif effect == "digital_noise":
                    processed_frames = [glitch_fx.apply_digital_noise(f, 0.05 + mult * 0.35) for f in processed_frames]
                elif effect == "rgb_shift":
                    shift = int(1 + mult * 12)
                    processed_frames = [glitch_fx.apply_rgb_shift(f, shift) for f in processed_frames]
                elif effect == "chromatic_aberration":
                    processed_frames = [color_fx.apply_chromatic_aberration(f, 0.1 + mult * 0.9) for f in processed_frames]
                elif effect == "liquid_distortion":
                    visc = 0.2 + mult * 0.6
                    turb = 0.1 + mult * 0.7
                    processed_frames = [warp_fx.apply_liquid_distortion(f, visc, 0.0, turb) for f in processed_frames]
                elif effect == "vortex_distortion":
                    strength = 0.3 + mult * 1.7
                    processed_frames = [warp_fx.apply_vortex_distortion(f, 0.5, 0.5, strength, 0.7, True) for f in processed_frames]
                elif effect == "datamosh":
                    if len(processed_frames) > 1:
                        processed_frames = datamosh_fx.apply_datamosh(processed_frames, 0.1 + mult * 0.7)

            return processed_frames

        except Exception as e:
            logger.error(f"Error applying effects: {e}")
            return frames

    def create_long_film(self, plan: Dict[str, Any], frames: List[np.ndarray], output_path: Path) -> bool:
        """Create a 30+ second film based on the plan."""
        logger.info(f"🎬 Creating long film: {output_path.name}")

        segments = plan.get('segments', [])
        if not segments:
            logger.error("No segments in plan")
            return False

        # Process segments
        all_processed_frames = []
        total_weight = sum(seg.get('duration_weight', 1.0) for seg in segments)

        for segment in segments:
            title = segment.get('title', f"Segment {segment['segment_id']}")
            logger.info(f"Processing segment: {title}")

            # Calculate frames for this segment
            weight = segment.get('duration_weight', 1.0)
            segment_frames = max(5, int(len(frames) * weight / total_weight))

            # Get frames for this segment
            start_idx = len(all_processed_frames)
            end_idx = min(start_idx + segment_frames, len(frames))

            if start_idx >= len(frames):
                # Reuse frames if we've run out
                start_idx = start_idx % len(frames)
                end_idx = min(start_idx + segment_frames, len(frames))

            segment_frame_list = frames[start_idx:end_idx]

            if not segment_frame_list:
                # Fallback: use first few frames
                segment_frame_list = frames[:min(5, len(frames))]

            # Apply effects
            effects = segment.get('effects', [])
            intensity = segment.get('intensity', 'moderate')

            processed_segment = self.apply_sophisticated_effects(segment_frame_list, effects, intensity)
            all_processed_frames.extend(processed_segment)

            logger.info(f"  {len(processed_segment)} frames, effects: {effects}, intensity: {intensity}")

        if not all_processed_frames:
            logger.error("No processed frames")
            return False

        # Ensure minimum 30 seconds at 24fps (720 frames)
        target_frames = 720  # 30 seconds at 24fps

        if len(all_processed_frames) < target_frames:
            # Repeat frames to reach target duration
            repeat_factor = target_frames // len(all_processed_frames) + 1
            extended_frames = []

            for _ in range(repeat_factor):
                extended_frames.extend(all_processed_frames)
                if len(extended_frames) >= target_frames:
                    break

            all_processed_frames = extended_frames[:target_frames]
            logger.info(f"Extended to {len(all_processed_frames)} frames for 30+ second duration")

        # Save high quality video
        return self._save_high_quality_video(all_processed_frames, output_path, fps=24)

    def _save_high_quality_video(self, frames: List[np.ndarray], output_path: Path, fps: int = 24) -> bool:
        """Save high quality video."""
        if not frames:
            return False

        try:
            height, width = frames[0].shape[:2]

            # Try multiple codecs for best compatibility
            codecs = ['mp4v', 'XVID', 'MJPG']

            for codec in codecs:
                try:
                    fourcc = cv2.VideoWriter_fourcc(*codec)
                    out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

                    if out.isOpened():
                        logger.info(f"Using codec: {codec}")

                        for frame in frames:
                            if len(frame.shape) == 3:
                                out.write(frame)
                            else:
                                bgr_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
                                out.write(bgr_frame)

                        out.release()

                        if output_path.exists() and output_path.stat().st_size > 0:
                            duration = len(frames) / fps
                            size_mb = output_path.stat().st_size / (1024 * 1024)
                            logger.info(f"Video saved: {output_path} ({size_mb:.1f}MB, {duration:.1f}s)")
                            return True

                except Exception as e:
                    logger.warning(f"Codec {codec} failed: {e}")
                    continue

            logger.error("All codecs failed")
            return False

        except Exception as e:
            logger.error(f"Error saving video: {e}")
            return False


def main():
    """Main function to run the comprehensive film system."""
    logger.info("🚀 COMPREHENSIVE FILM SYSTEM - PySceneDetect + 30+ Second Films")

    try:
        # Initialize system
        system = ComprehensiveFilmSystem()

        # Get video directory
        video_dir = Path("C:/Quick Share")
        if not video_dir.exists():
            logger.error(f"Video directory not found: {video_dir}")
            return False

        # Create output directory
        output_dir = Path("./output")
        output_dir.mkdir(exist_ok=True)

        # Step 1: Detect scenes in all videos using PySceneDetect
        logger.info("\n" + "="*70)
        logger.info("STEP 1: Scene Detection with PySceneDetect")
        logger.info("="*70)

        all_video_scenes = system.detect_scenes_all_videos(video_dir)

        if not all_video_scenes:
            logger.error("No scenes detected in any videos")
            return False

        # Step 2: Analyze all scenes with Gemini and store results
        logger.info("\n" + "="*70)
        logger.info("STEP 2: Comprehensive Scene Analysis with Gemini")
        logger.info("="*70)

        analysis_results = system.analyze_scenes_with_gemini(all_video_scenes)

        if analysis_results['analyzed_scenes'] == 0:
            logger.error("No scenes were analyzed")
            return False

        # Step 3: Extract frames from all videos
        logger.info("\n" + "="*70)
        logger.info("STEP 3: Frame Extraction from All Videos")
        logger.info("="*70)

        all_frames = system.extract_frames_for_film(analysis_results, target_frames=300)

        if not all_frames:
            logger.error("No frames extracted")
            return False

        # Step 4: Create multiple 30+ second experimental films
        logger.info("\n" + "="*70)
        logger.info("STEP 4: Creating 30+ Second Experimental Films")
        logger.info("="*70)

        film_concepts = [
            ("digital consciousness emergence", "cyberpunk"),
            ("memory dissolution cascade", "surreal"),
            ("reality fragmentation matrix", "glitch"),
            ("temporal distortion field", "abstract"),
            ("consciousness data stream", "futuristic")
        ]

        created_films = []

        for concept, style in film_concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎭 CREATING: '{concept}' ({style})")
            logger.info(f"{'='*60}")

            # Create film plan
            plan = system.create_film_plan(concept, style, analysis_results)

            if plan.get('error'):
                logger.error(f"Plan creation failed: {plan['error']}")
                continue

            segments = plan.get('segments', [])
            estimated_duration = plan.get('estimated_duration', 0)

            logger.info(f"Plan created: {len(segments)} segments, estimated {estimated_duration:.1f}s")

            # Create film
            safe_concept = concept.replace(' ', '_').replace('/', '_')
            output_path = output_dir / f"comprehensive_{safe_concept}.mp4"

            success = system.create_long_film(plan, all_frames, output_path)

            if success:
                logger.info(f"✅ CREATED: {output_path}")
                created_films.append({
                    'concept': concept,
                    'style': style,
                    'path': str(output_path),
                    'segments': len(segments),
                    'estimated_duration': estimated_duration
                })

                # Save plan details
                plan_path = output_dir / f"comprehensive_plan_{safe_concept}.json"
                with open(plan_path, 'w') as f:
                    json.dump({
                        'concept': concept,
                        'style': style,
                        'plan': plan,
                        'analysis_summary': {
                            'total_videos': len(analysis_results['videos']),
                            'total_scenes': analysis_results['total_scenes'],
                            'analyzed_scenes': analysis_results['analyzed_scenes']
                        }
                    }, f, indent=2)

                logger.info(f"📋 Plan saved: {plan_path}")

            else:
                logger.error(f"❌ FAILED: {output_path}")

        # Final summary
        logger.info(f"\n{'='*70}")
        logger.info("🎉 COMPREHENSIVE SYSTEM COMPLETE!")
        logger.info(f"{'='*70}")

        if created_films:
            logger.info(f"Created {len(created_films)} experimental films:")
            for film in created_films:
                logger.info(f"📽️  {film['concept']}")
                logger.info(f"    Path: {film['path']}")
                logger.info(f"    Style: {film['style']}")
                logger.info(f"    Segments: {film['segments']}")
                logger.info(f"    Duration: ~{film['estimated_duration']:.1f}s")

            logger.info(f"\n🎬 SYSTEM FEATURES:")
            logger.info(f"🔍 PySceneDetect for all videos in directory")
            logger.info(f"🤖 Gemini API content analysis and storage")
            logger.info(f"📋 Intelligent high-level planning")
            logger.info(f"🎨 Sophisticated effect application")
            logger.info(f"⏱️  30+ second film duration")
            logger.info(f"📹 High quality video output")

            return True
        else:
            logger.error("No films were created")
            return False

    except Exception as e:
        logger.error(f"System failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
