{"concept": "fractured reality", "style": "glitch", "segments": [{"segment_id": 1, "description": "Initial sterile observation of fractured reality", "mood": "unease", "duration_weight": 1.0, "effects": [{"function_name": "digital_noise", "parameters": {"intensity": 0.1, "grain_size": 2}, "target_frames": "all"}]}, {"segment_id": 2, "description": "Reality begins to subtly shift, mirroring the repetition of the scenes", "mood": "disorientation", "duration_weight": 1.5, "effects": [{"function_name": "wave_distortion", "parameters": {"amplitude": 5, "frequency": 0.5}, "target_frames": "second_half"}, {"function_name": "color_shift", "parameters": {"hue_shift": 10, "saturation_shift": -10}, "target_frames": "random_subset"}]}, {"segment_id": 3, "description": "The fracture intensifies, showing the instability of reality", "mood": "anxiety", "duration_weight": 1.8, "effects": [{"function_name": "mirror_effect", "parameters": {}, "target_frames": "all"}, {"function_name": "flicker", "parameters": {"frequency": 2, "intensity": 0.5}, "target_frames": "all"}]}, {"segment_id": 4, "description": "The fractured reality collapses into static", "mood": "emptiness", "duration_weight": 0.7, "effects": [{"function_name": "digital_noise", "parameters": {"intensity": 0.8, "grain_size": 8}, "target_frames": "all"}, {"function_name": "fade_to_black", "parameters": {}, "target_frames": "second_half"}]}], "video_analysis_summary": {"total_scenes": 3, "total_duration": 3.332042209807573, "average_scene_duration": 1.1106807366025244, "change_type_distribution": {"optical_flow": 2, "color_moments": 1}, "scenes_with_content": 3, "high_confidence_scenes": 3}}