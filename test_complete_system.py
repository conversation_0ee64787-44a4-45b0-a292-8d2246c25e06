"""
Complete system test for enhanced effects and scene detection.
"""

import logging
import cv2
import numpy as np
from pathlib import Path
import json
from typing import List
import traceback

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def save_frames_as_video(frames: List[np.ndarray], output_path: Path, fps: int = 24) -> bool:
    """Save frames as video."""
    if not frames:
        return False

    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

        if not out.isOpened():
            return False

        for frame in frames:
            out.write(frame)

        out.release()
        return output_path.exists()

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False


def test_enhanced_effects():
    """Test the enhanced effects with new parameters."""
    logger.info("🎨 Testing Enhanced Effects")
    
    try:
        from effects.frame_warping import FrameWarpingEffect
        from video_analysis.frame_extractor import FrameExtractor
        
        # Get test video
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found for testing")
            return False
        
        source_video = video_files[0]
        logger.info(f"Using test video: {source_video.name}")
        
        # Extract frames
        extractor = FrameExtractor()
        frames = extractor.extract_frames(source_video, start_time=0, end_time=2)
        frame_list = [frame for _, frame in frames[:20]]
        
        if not frame_list:
            logger.error("No frames extracted")
            return False
        
        logger.info(f"Extracted {len(frame_list)} frames")
        
        # Initialize effects
        warp_fx = FrameWarpingEffect()
        
        # Test enhanced wave distortion
        logger.info("Testing enhanced wave distortion...")
        enhanced_frames = []
        for i, frame in enumerate(frame_list):
            # Test different wave types and directions
            if i < 5:
                # Radial sine waves
                processed = warp_fx.apply_wave_distortion(
                    frame, amplitude=30, frequency=0.15, direction="radial",
                    wave_type="sine", center_x=0.3, center_y=0.7, falloff=0.8
                )
            elif i < 10:
                # Spiral square waves
                processed = warp_fx.apply_wave_distortion(
                    frame, amplitude=25, frequency=0.2, direction="spiral",
                    wave_type="square", center_x=0.7, center_y=0.3
                )
            elif i < 15:
                # Liquid distortion
                processed = warp_fx.apply_liquid_distortion(
                    frame, viscosity=0.7, flow_direction=1.57, turbulence=0.5,
                    time_factor=i * 0.2
                )
            else:
                # Vortex distortion
                processed = warp_fx.apply_vortex_distortion(
                    frame, center_x=0.5, center_y=0.5, strength=1.5,
                    radius=0.6, clockwise=(i % 2 == 0)
                )
            
            enhanced_frames.append(processed)
        
        # Save result
        output_path = Path("./output/enhanced_effects_test.mp4")
        output_path.parent.mkdir(exist_ok=True)
        
        if save_frames_as_video(enhanced_frames, output_path):
            logger.info(f"✅ Enhanced effects test saved: {output_path}")
            return True
        else:
            logger.error("❌ Failed to save enhanced effects test")
            return False
            
    except Exception as e:
        logger.error(f"Enhanced effects test failed: {e}")
        traceback.print_exc()
        return False


def test_scene_detection():
    """Test the advanced scene detection system."""
    logger.info("🔍 Testing Advanced Scene Detection")
    
    try:
        from video_analysis.advanced_scene_detection import AdvancedSceneDetector
        
        # Get test video
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found for testing")
            return False
        
        source_video = video_files[0]
        logger.info(f"Analyzing video: {source_video.name}")
        
        # Initialize detector
        detector = AdvancedSceneDetector()
        
        # Detect scenes using multiple methods
        scenes = detector.detect_scenes(
            source_video, 
            methods=['histogram', 'optical_flow', 'edge_density', 'color_moments']
        )
        
        if not scenes:
            logger.warning("No scenes detected")
            return False
        
        # Get summary
        summary = detector.get_scene_summary(scenes)
        
        logger.info(f"Scene Detection Results:")
        logger.info(f"  Total scenes: {summary['total_scenes']}")
        logger.info(f"  Total duration: {summary['total_duration']:.1f}s")
        logger.info(f"  Average scene duration: {summary['average_scene_duration']:.1f}s")
        logger.info(f"  High confidence scenes: {summary['high_confidence_scenes']}")
        
        # Print scene details
        for scene in scenes[:5]:  # Show first 5 scenes
            logger.info(f"Scene {scene.scene_id}: {scene.start_time:.1f}s-{scene.end_time:.1f}s "
                       f"({scene.change_type}, confidence: {scene.confidence:.2f})")
            if scene.content_description:
                logger.info(f"  Content: {scene.content_description[:100]}...")
        
        # Save scene analysis
        scene_data = {
            'summary': summary,
            'scenes': [
                {
                    'scene_id': s.scene_id,
                    'start_time': s.start_time,
                    'end_time': s.end_time,
                    'duration': s.duration,
                    'confidence': s.confidence,
                    'change_type': s.change_type,
                    'content_description': s.content_description,
                    'visual_features': s.visual_features
                }
                for s in scenes
            ]
        }
        
        analysis_path = Path("./output/scene_analysis.json")
        analysis_path.parent.mkdir(exist_ok=True)
        
        with open(analysis_path, 'w') as f:
            json.dump(scene_data, f, indent=2)
        
        logger.info(f"✅ Scene analysis saved: {analysis_path}")
        return True
        
    except Exception as e:
        logger.error(f"Scene detection test failed: {e}")
        traceback.print_exc()
        return False


def test_gemini_function_planner():
    """Test the Gemini function calling planner."""
    logger.info("🤖 Testing Gemini Function Planner")
    
    try:
        from gemini_function_planner import GeminiFunctionPlanner
        from video_analysis.frame_extractor import FrameExtractor
        
        # Get test video
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found for testing")
            return False
        
        source_video = video_files[0]
        logger.info(f"Using video: {source_video.name}")
        
        # Initialize planner
        planner = GeminiFunctionPlanner()
        
        # Analyze video content
        logger.info("Analyzing video content...")
        video_analysis = planner.analyze_video_content(source_video, max_scenes=3)
        
        if video_analysis.get('error'):
            logger.error(f"Video analysis failed: {video_analysis['error']}")
            return False
        
        logger.info(f"Analyzed {video_analysis['analyzed_scenes']} scenes")
        
        # Extract frames for processing
        extractor = FrameExtractor()
        frames = extractor.extract_frames(source_video, start_time=0, end_time=4)
        frame_list = [frame for _, frame in frames[:30]]
        
        if not frame_list:
            logger.error("No frames extracted")
            return False
        
        logger.info(f"Extracted {len(frame_list)} frames for processing")
        
        # Test different concepts
        concepts = [
            ("digital metamorphosis", "cyberpunk"),
            ("liquid memories", "dreamy"),
            ("fractured reality", "glitch")
        ]
        
        for concept, style in concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎬 Creating plan for: '{concept}' ({style})")
            logger.info(f"{'='*60}")
            
            # Create content-aware plan
            plan = planner.create_content_aware_plan(concept, style, video_analysis)
            
            if plan.get('error'):
                logger.error(f"Plan creation failed: {plan['error']}")
                continue
            
            # Log plan details
            logger.info(f"Plan created with {len(plan['segments'])} segments:")
            for segment in plan['segments']:
                logger.info(f"  Segment {segment.segment_id}: {segment.description}")
                logger.info(f"    Mood: {segment.mood}")
                logger.info(f"    Effects: {[e.function_name for e in segment.effects]}")
            
            # Execute plan
            logger.info("Executing plan...")
            processed_frames = planner.execute_plan(plan, frame_list.copy())
            
            # Save result
            safe_concept = concept.replace(' ', '_').replace('/', '_')
            output_path = Path(f"./output/gemini_planned_{safe_concept}.mp4")
            output_path.parent.mkdir(exist_ok=True)
            
            if save_frames_as_video(processed_frames, output_path):
                logger.info(f"✅ Created: {output_path}")
            else:
                logger.error(f"❌ Failed to save: {output_path}")
            
            # Save plan details
            plan_path = Path(f"./output/plan_{safe_concept}.json")
            plan_data = {
                'concept': concept,
                'style': style,
                'segments': [
                    {
                        'segment_id': s.segment_id,
                        'description': s.description,
                        'mood': s.mood,
                        'duration_weight': s.duration_weight,
                        'effects': [
                            {
                                'function_name': e.function_name,
                                'parameters': e.parameters,
                                'target_frames': e.target_frames
                            }
                            for e in s.effects
                        ]
                    }
                    for s in plan['segments']
                ],
                'video_analysis_summary': video_analysis.get('summary', {})
            }
            
            with open(plan_path, 'w') as f:
                json.dump(plan_data, f, indent=2)
            
            logger.info(f"📋 Plan saved: {plan_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Gemini planner test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    logger.info("🚀 Starting Complete System Test")
    
    # Create output directory
    Path("./output").mkdir(exist_ok=True)
    
    results = {}
    
    # Test 1: Enhanced Effects
    results['enhanced_effects'] = test_enhanced_effects()
    
    # Test 2: Scene Detection
    results['scene_detection'] = test_scene_detection()
    
    # Test 3: Gemini Function Planner
    results['gemini_planner'] = test_gemini_function_planner()
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("🎯 TEST RESULTS SUMMARY")
    logger.info(f"{'='*60}")
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    logger.info(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        logger.info("🎉 ALL TESTS PASSED! System is ready for film creation.")
        return True
    else:
        logger.warning("⚠️  Some tests failed. Check logs for details.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
