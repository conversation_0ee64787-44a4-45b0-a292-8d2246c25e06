"""
Advanced screen splitting effects for multi-viewport compositions.
"""

import cv2
import numpy as np
import math
import random
from typing import List, Tu<PERSON>, Dict, Any
import logging

logger = logging.getLogger(__name__)


class ScreenSplittingEffects:
    """Advanced screen splitting and viewport effects."""
    
    def __init__(self):
        self.split_cache = {}
    
    def apply_grid_split(self, frame: np.ndarray, rows: int = 2, cols: int = 2, 
                        border_width: int = 2, border_color: Tuple[int, int, int] = (0, 0, 0)) -> np.ndarray:
        """
        Split frame into regular grid like browser tabs or phone apps.
        
        Args:
            frame: Input frame
            rows: Number of rows
            cols: Number of columns  
            border_width: Width of borders between panels
            border_color: Color of borders (BGR)
            
        Returns:
            Grid-split frame
        """
        height, width = frame.shape[:2]
        
        # Calculate panel dimensions
        panel_height = (height - border_width * (rows - 1)) // rows
        panel_width = (width - border_width * (cols - 1)) // cols
        
        result = np.zeros_like(frame)
        
        for row in range(rows):
            for col in range(cols):
                # Calculate source region (scaled down version of full frame)
                src_frame = cv2.resize(frame, (panel_width, panel_height))
                
                # Calculate destination position
                y_start = row * (panel_height + border_width)
                x_start = col * (panel_width + border_width)
                y_end = y_start + panel_height
                x_end = x_start + panel_width
                
                # Place panel
                if y_end <= height and x_end <= width:
                    result[y_start:y_end, x_start:x_end] = src_frame
                
                # Add borders
                if border_width > 0:
                    # Right border
                    if x_end + border_width <= width:
                        result[y_start:y_end, x_end:x_end+border_width] = border_color
                    # Bottom border  
                    if y_end + border_width <= height:
                        result[y_end:y_end+border_width, x_start:x_end] = border_color
        
        return result
    
    def apply_irregular_split(self, frame: np.ndarray, split_type: str = "diagonal", 
                             num_panels: int = 4, randomness: float = 0.3) -> np.ndarray:
        """
        Apply irregular, dynamic splits.
        
        Args:
            frame: Input frame
            split_type: "diagonal", "circular", "spiral", "organic"
            num_panels: Number of panels to create
            randomness: Amount of randomness in splits (0.0-1.0)
            
        Returns:
            Irregularly split frame
        """
        height, width = frame.shape[:2]
        result = np.zeros_like(frame)
        
        if split_type == "diagonal":
            return self._apply_diagonal_split(frame, num_panels, randomness)
        elif split_type == "circular":
            return self._apply_circular_split(frame, num_panels, randomness)
        elif split_type == "spiral":
            return self._apply_spiral_split(frame, num_panels, randomness)
        elif split_type == "organic":
            return self._apply_organic_split(frame, num_panels, randomness)
        else:
            return frame
    
    def _apply_diagonal_split(self, frame: np.ndarray, num_panels: int, randomness: float) -> np.ndarray:
        """Apply diagonal tears and splits."""
        height, width = frame.shape[:2]
        result = np.zeros_like(frame)
        
        # Create diagonal split lines
        for i in range(num_panels):
            # Random diagonal angle
            angle = (i * 180 / num_panels) + random.uniform(-30, 30) * randomness
            
            # Create mask for this panel
            mask = np.zeros((height, width), dtype=np.uint8)
            
            # Calculate diagonal line
            center_x, center_y = width // 2, height // 2
            line_length = max(width, height)
            
            x1 = int(center_x - line_length * math.cos(math.radians(angle)))
            y1 = int(center_y - line_length * math.sin(math.radians(angle)))
            x2 = int(center_x + line_length * math.cos(math.radians(angle)))
            y2 = int(center_y + line_length * math.sin(math.radians(angle)))
            
            # Create polygon for this section
            if i == 0:
                points = np.array([[0, 0], [width, 0], [x2, y2], [x1, y1]], dtype=np.int32)
            else:
                # Create wedge sections
                prev_angle = ((i-1) * 180 / num_panels) + random.uniform(-30, 30) * randomness
                prev_x2 = int(center_x + line_length * math.cos(math.radians(prev_angle)))
                prev_y2 = int(center_y + line_length * math.sin(math.radians(prev_angle)))
                
                points = np.array([[center_x, center_y], [prev_x2, prev_y2], [x2, y2]], dtype=np.int32)
            
            cv2.fillPoly(mask, [points], 255)
            
            # Apply different transformations to each panel
            panel_frame = frame.copy()
            if i % 2 == 0:
                panel_frame = cv2.flip(panel_frame, 1)  # Horizontal flip
            if i % 3 == 0:
                panel_frame = cv2.rotate(panel_frame, cv2.ROTATE_90_CLOCKWISE)
                panel_frame = cv2.resize(panel_frame, (width, height))
            
            # Apply mask
            mask_3d = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
            result = result + (panel_frame * mask_3d).astype(np.uint8)
        
        return result
    
    def _apply_circular_split(self, frame: np.ndarray, num_panels: int, randomness: float) -> np.ndarray:
        """Apply circular viewport splits."""
        height, width = frame.shape[:2]
        result = np.zeros_like(frame)
        
        # Create circular viewports
        for i in range(num_panels):
            # Random position and size
            center_x = int(width * (0.2 + 0.6 * random.random()))
            center_y = int(height * (0.2 + 0.6 * random.random()))
            radius = int(min(width, height) * (0.1 + 0.3 * random.random()))
            
            # Create circular mask
            mask = np.zeros((height, width), dtype=np.uint8)
            cv2.circle(mask, (center_x, center_y), radius, 255, -1)
            
            # Apply different effects to each viewport
            panel_frame = frame.copy()
            
            # Scale and position content differently
            scale = 0.5 + 0.5 * random.random()
            scaled_frame = cv2.resize(panel_frame, None, fx=scale, fy=scale)
            
            # Center the scaled frame
            if scaled_frame.shape[0] < height or scaled_frame.shape[1] < width:
                temp_frame = np.zeros_like(frame)
                y_offset = (height - scaled_frame.shape[0]) // 2
                x_offset = (width - scaled_frame.shape[1]) // 2
                temp_frame[y_offset:y_offset+scaled_frame.shape[0], 
                          x_offset:x_offset+scaled_frame.shape[1]] = scaled_frame
                panel_frame = temp_frame
            else:
                panel_frame = scaled_frame[:height, :width]
            
            # Apply mask
            mask_3d = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
            result = result + (panel_frame * mask_3d).astype(np.uint8)
        
        return result
    
    def _apply_spiral_split(self, frame: np.ndarray, num_panels: int, randomness: float) -> np.ndarray:
        """Apply spiral inward splits."""
        height, width = frame.shape[:2]
        result = np.zeros_like(frame)
        
        center_x, center_y = width // 2, height // 2
        max_radius = min(width, height) // 2
        
        for i in range(num_panels):
            # Create spiral mask
            mask = np.zeros((height, width), dtype=np.uint8)
            
            # Spiral parameters
            start_angle = i * 360 / num_panels
            end_angle = (i + 1) * 360 / num_panels
            
            # Create spiral points
            points = []
            for angle in range(int(start_angle), int(end_angle), 5):
                radius = max_radius * (1 - angle / 360)
                x = int(center_x + radius * math.cos(math.radians(angle)))
                y = int(center_y + radius * math.sin(math.radians(angle)))
                points.append([x, y])
            
            if len(points) > 2:
                points = np.array(points, dtype=np.int32)
                cv2.fillPoly(mask, [points], 255)
                
                # Apply transformation
                panel_frame = frame.copy()
                rotation_angle = i * 45
                M = cv2.getRotationMatrix2D((center_x, center_y), rotation_angle, 1.0)
                panel_frame = cv2.warpAffine(panel_frame, M, (width, height))
                
                # Apply mask
                mask_3d = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
                result = result + (panel_frame * mask_3d).astype(np.uint8)
        
        return result
    
    def _apply_organic_split(self, frame: np.ndarray, num_panels: int, randomness: float) -> np.ndarray:
        """Apply organic, flowing splits."""
        height, width = frame.shape[:2]
        result = np.zeros_like(frame)
        
        # Create organic shapes using noise
        for i in range(num_panels):
            mask = np.zeros((height, width), dtype=np.uint8)
            
            # Generate organic shape
            num_points = 8 + int(randomness * 12)
            points = []
            
            center_x = width * (0.3 + 0.4 * random.random())
            center_y = height * (0.3 + 0.4 * random.random())
            base_radius = min(width, height) * (0.2 + 0.3 * random.random())
            
            for j in range(num_points):
                angle = j * 2 * math.pi / num_points
                radius_variation = 1 + randomness * (random.random() - 0.5)
                radius = base_radius * radius_variation
                
                x = int(center_x + radius * math.cos(angle))
                y = int(center_y + radius * math.sin(angle))
                points.append([x, y])
            
            points = np.array(points, dtype=np.int32)
            cv2.fillPoly(mask, [points], 255)
            
            # Apply transformation
            panel_frame = frame.copy()
            
            # Random color shift
            hsv = cv2.cvtColor(panel_frame, cv2.COLOR_BGR2HSV)
            hsv[:, :, 0] = (hsv[:, :, 0] + i * 30) % 180
            panel_frame = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
            
            # Apply mask
            mask_3d = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
            result = result + (panel_frame * mask_3d).astype(np.uint8)
        
        return result

    def apply_screen_bleeding(self, frames: List[np.ndarray], bleed_intensity: float = 0.3,
                             bleed_type: str = "edge") -> List[np.ndarray]:
        """
        Apply screen bleeding where content spills between panels.

        Args:
            frames: List of frames to process
            bleed_intensity: Intensity of bleeding effect (0.0-1.0)
            bleed_type: "edge", "flash", "overflow"

        Returns:
            Frames with bleeding effect
        """
        if len(frames) < 2:
            return frames

        result_frames = []

        for i, frame in enumerate(frames):
            if bleed_type == "edge":
                result = self._apply_edge_bleeding(frame, frames, i, bleed_intensity)
            elif bleed_type == "flash":
                result = self._apply_flash_bleeding(frame, frames, i, bleed_intensity)
            elif bleed_type == "overflow":
                result = self._apply_overflow_bleeding(frame, frames, i, bleed_intensity)
            else:
                result = frame

            result_frames.append(result)

        return result_frames

    def _apply_edge_bleeding(self, frame: np.ndarray, all_frames: List[np.ndarray],
                           frame_idx: int, intensity: float) -> np.ndarray:
        """Apply edge bleeding between adjacent panels."""
        height, width = frame.shape[:2]
        result = frame.copy()

        # Get adjacent frame
        next_frame = all_frames[(frame_idx + 1) % len(all_frames)]

        # Create bleeding mask (edges)
        mask = np.zeros((height, width), dtype=np.float32)

        # Add bleeding from edges
        edge_width = int(width * 0.1 * intensity)

        # Right edge bleeding
        mask[:, -edge_width:] = np.linspace(0, intensity, edge_width)

        # Left edge bleeding
        mask[:, :edge_width] = np.linspace(intensity, 0, edge_width)

        # Apply bleeding
        mask_3d = np.stack([mask, mask, mask], axis=2)
        result = result.astype(np.float32)
        next_frame = next_frame.astype(np.float32)

        result = result * (1 - mask_3d) + next_frame * mask_3d

        return np.clip(result, 0, 255).astype(np.uint8)

    def _apply_flash_bleeding(self, frame: np.ndarray, all_frames: List[np.ndarray],
                            frame_idx: int, intensity: float) -> np.ndarray:
        """Apply flash bleeding (bright areas spill over)."""
        height, width = frame.shape[:2]
        result = frame.copy()

        # Detect bright areas (flashes)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        _, bright_mask = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)

        # Dilate bright areas to create bleeding
        kernel = np.ones((15, 15), np.uint8)
        bleeding_mask = cv2.dilate(bright_mask, kernel, iterations=2)
        bleeding_mask = bleeding_mask.astype(np.float32) / 255.0 * intensity

        # Get next frame for bleeding source
        next_frame = all_frames[(frame_idx + 1) % len(all_frames)]

        # Apply flash bleeding
        mask_3d = np.stack([bleeding_mask, bleeding_mask, bleeding_mask], axis=2)
        result = result.astype(np.float32)
        next_frame = next_frame.astype(np.float32)

        # Brighten the bleeding areas
        bright_bleed = np.minimum(next_frame * 1.5, 255)
        result = result * (1 - mask_3d) + bright_bleed * mask_3d

        return np.clip(result, 0, 255).astype(np.uint8)

    def _apply_overflow_bleeding(self, frame: np.ndarray, all_frames: List[np.ndarray],
                               frame_idx: int, intensity: float) -> np.ndarray:
        """Apply overflow bleeding (content spills randomly)."""
        height, width = frame.shape[:2]
        result = frame.copy()

        # Create random overflow areas
        num_overflows = random.randint(2, 6)

        for _ in range(num_overflows):
            # Random overflow region
            x = random.randint(0, width - 50)
            y = random.randint(0, height - 50)
            w = random.randint(30, 100)
            h = random.randint(30, 100)

            # Get source from different frame
            source_idx = random.randint(0, len(all_frames) - 1)
            source_frame = all_frames[source_idx]

            # Random source region
            src_x = random.randint(0, max(1, width - w))
            src_y = random.randint(0, max(1, height - h))

            # Apply overflow with blending
            alpha = intensity * random.uniform(0.3, 0.8)

            try:
                overflow_region = source_frame[src_y:src_y+h, src_x:src_x+w]
                if overflow_region.size > 0:
                    result[y:y+h, x:x+w] = cv2.addWeighted(
                        result[y:y+h, x:x+w], 1-alpha,
                        overflow_region, alpha, 0
                    )
            except:
                continue

        return result

    def apply_recursive_splitting(self, frame: np.ndarray, depth: int = 3,
                                scale_factor: float = 0.3) -> np.ndarray:
        """
        Apply recursive splitting (screens within screens).

        Args:
            frame: Input frame
            depth: Recursion depth
            scale_factor: Scale factor for each recursion level

        Returns:
            Recursively split frame
        """
        if depth <= 0:
            return frame

        height, width = frame.shape[:2]
        result = frame.copy()

        # Create smaller version for recursion
        small_width = int(width * scale_factor)
        small_height = int(height * scale_factor)
        small_frame = cv2.resize(frame, (small_width, small_height))

        # Apply recursive effect to smaller frame
        recursive_frame = self.apply_recursive_splitting(small_frame, depth - 1, scale_factor)
        recursive_frame = cv2.resize(recursive_frame, (small_width, small_height))

        # Place recursive frames in corners and center
        positions = [
            (10, 10),  # Top-left
            (width - small_width - 10, 10),  # Top-right
            (10, height - small_height - 10),  # Bottom-left
            (width - small_width - 10, height - small_height - 10),  # Bottom-right
            (width//2 - small_width//2, height//2 - small_height//2)  # Center
        ]

        for i, (x, y) in enumerate(positions[:min(depth, len(positions))]):
            if x + small_width <= width and y + small_height <= height:
                # Add some variation to each recursive instance
                temp_frame = recursive_frame.copy()
                if i % 2 == 0:
                    temp_frame = cv2.flip(temp_frame, 1)
                if i % 3 == 0:
                    # Slight color shift
                    hsv = cv2.cvtColor(temp_frame, cv2.COLOR_BGR2HSV)
                    hsv[:, :, 0] = (hsv[:, :, 0] + i * 20) % 180
                    temp_frame = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

                # Blend with main frame
                result[y:y+small_height, x:x+small_width] = cv2.addWeighted(
                    result[y:y+small_height, x:x+small_width], 0.7,
                    temp_frame, 0.3, 0
                )

        return result

    def apply_micro_fragmentation(self, frame: np.ndarray, fragment_count: int = 50,
                                min_size: int = 10, max_size: int = 40) -> np.ndarray:
        """
        Fragment into dozens of tiny screens showing micro-moments.

        Args:
            frame: Input frame
            fragment_count: Number of fragments
            min_size: Minimum fragment size
            max_size: Maximum fragment size

        Returns:
            Micro-fragmented frame
        """
        height, width = frame.shape[:2]
        result = np.zeros_like(frame)

        # Create random fragments
        for i in range(fragment_count):
            # Random fragment size and position
            frag_size = random.randint(min_size, max_size)
            x = random.randint(0, width - frag_size)
            y = random.randint(0, height - frag_size)

            # Random source region (micro-moment)
            src_x = random.randint(0, width - frag_size)
            src_y = random.randint(0, height - frag_size)

            # Extract micro-moment
            fragment = frame[src_y:src_y+frag_size, src_x:src_x+frag_size]

            # Apply micro-effect to fragment
            if random.random() < 0.3:  # 30% chance of effect
                if random.random() < 0.5:
                    # High contrast (angry emoji effect)
                    fragment = cv2.convertScaleAbs(fragment, alpha=2.0, beta=-50)
                else:
                    # Color inversion (negative effect)
                    fragment = 255 - fragment

            # Place fragment
            result[y:y+frag_size, x:x+frag_size] = fragment

            # Add border for separation
            cv2.rectangle(result, (x, y), (x+frag_size, y+frag_size), (255, 255, 255), 1)

        return result
