"""
Frame warping and geometric distortion effects.
"""

import logging
import numpy as np
import cv2
from typing import Tuple, Optional, List
import random
import math

try:
    from config import config
except ImportError:
    # Fallback config
    class Config:
        class effects:
            warp_intensity = 0.2
            recursive_depth = 3
    config = Config()

logger = logging.getLogger(__name__)


class FrameWarpingEffect:
    """Implements various frame warping and geometric distortion effects."""

    def __init__(self):
        self.warp_intensity = config.effects.warp_intensity
        self.recursive_depth = config.effects.recursive_depth

    def apply_wave_distortion(self, frame: np.ndarray, amplitude: float = 20, frequency: float = 0.1,
                             direction: str = "horizontal", phase: float = 0.0,
                             wave_type: str = "sine", center_x: float = 0.5, center_y: float = 0.5,
                             falloff: float = 1.0, time_offset: float = 0.0) -> np.ndarray:
        """
        Apply wave distortion to frame with enhanced parameters.

        Args:
            frame: Input frame
            amplitude: Wave amplitude
            frequency: Wave frequency
            direction: "horizontal", "vertical", "both", "radial", or "spiral"
            phase: Phase offset for the wave
            wave_type: "sine", "cosine", "square", "triangle", or "sawtooth"
            center_x: Center point X (normalized 0-1)
            center_y: Center point Y (normalized 0-1)
            falloff: Distance falloff factor for radial waves
            time_offset: Time-based animation offset

        Returns:
            Wave-distorted frame
        """
        height, width = frame.shape[:2]

        # Calculate center points
        center_px = int(center_x * width)
        center_py = int(center_y * height)

        # Create coordinate grids
        x, y = np.meshgrid(np.arange(width), np.arange(height))

        # Generate wave function based on type
        def wave_func(t):
            t = t + phase + time_offset
            if wave_type == "sine":
                return np.sin(t)
            elif wave_type == "cosine":
                return np.cos(t)
            elif wave_type == "square":
                return np.sign(np.sin(t))
            elif wave_type == "triangle":
                return 2 * np.arcsin(np.sin(t)) / np.pi
            elif wave_type == "sawtooth":
                return 2 * (t / (2 * np.pi) - np.floor(t / (2 * np.pi) + 0.5))
            else:
                return np.sin(t)

        # Apply wave distortion based on direction
        if direction == "horizontal":
            wave_input = 2 * np.pi * frequency * y / height
            x_new = x + amplitude * wave_func(wave_input)
            y_new = y
        elif direction == "vertical":
            wave_input = 2 * np.pi * frequency * x / width
            x_new = x
            y_new = y + amplitude * wave_func(wave_input)
        elif direction == "both":
            wave_input_x = 2 * np.pi * frequency * y / height
            wave_input_y = 2 * np.pi * frequency * x / width
            x_new = x + amplitude * wave_func(wave_input_x)
            y_new = y + amplitude * wave_func(wave_input_y)
        elif direction == "radial":
            # Distance from center
            dx = x - center_px
            dy = y - center_py
            distance = np.sqrt(dx*dx + dy*dy)
            max_distance = np.sqrt(center_px*center_px + center_py*center_py)

            # Apply falloff
            falloff_factor = np.exp(-distance / (max_distance * falloff)) if falloff > 0 else 1.0

            wave_input = 2 * np.pi * frequency * distance / max_distance
            wave_amplitude = amplitude * falloff_factor * wave_func(wave_input)

            # Apply radial distortion
            angle = np.arctan2(dy, dx)
            x_new = x + wave_amplitude * np.cos(angle)
            y_new = y + wave_amplitude * np.sin(angle)
        elif direction == "spiral":
            # Spiral distortion
            dx = x - center_px
            dy = y - center_py
            distance = np.sqrt(dx*dx + dy*dy)
            angle = np.arctan2(dy, dx)
            max_distance = np.sqrt(center_px*center_px + center_py*center_py)

            # Apply spiral wave
            wave_input = 2 * np.pi * frequency * (distance / max_distance + angle / (2 * np.pi))
            spiral_offset = amplitude * wave_func(wave_input)

            new_angle = angle + spiral_offset / max_distance
            x_new = center_px + distance * np.cos(new_angle)
            y_new = center_py + distance * np.sin(new_angle)
        else:  # default to horizontal
            wave_input = 2 * np.pi * frequency * y / height
            x_new = x + amplitude * wave_func(wave_input)
            y_new = y

        # Clamp coordinates
        x_new = np.clip(x_new, 0, width - 1)
        y_new = np.clip(y_new, 0, height - 1)

        # Apply distortion
        warped = cv2.remap(frame, x_new.astype(np.float32), y_new.astype(np.float32), cv2.INTER_LINEAR)

        return warped

    def apply_spiral_distortion(self, frame: np.ndarray, intensity: float = 0.5) -> np.ndarray:
        """
        Apply spiral distortion effect.

        Args:
            frame: Input frame
            intensity: Distortion intensity

        Returns:
            Spiral-distorted frame
        """
        height, width = frame.shape[:2]
        center_x, center_y = width // 2, height // 2

        # Create coordinate grids
        y, x = np.ogrid[:height, :width]

        # Calculate polar coordinates
        dx = x - center_x
        dy = y - center_y
        distance = np.sqrt(dx*dx + dy*dy)
        angle = np.arctan2(dy, dx)

        # Apply spiral distortion
        max_distance = np.sqrt(center_x*center_x + center_y*center_y)
        normalized_distance = distance / max_distance

        # Spiral effect
        angle_offset = intensity * normalized_distance * 4 * np.pi
        new_angle = angle + angle_offset

        # Convert back to Cartesian
        new_x = center_x + distance * np.cos(new_angle)
        new_y = center_y + distance * np.sin(new_angle)

        # Clamp coordinates
        new_x = np.clip(new_x, 0, width - 1)
        new_y = np.clip(new_y, 0, height - 1)

        # Apply distortion
        warped = cv2.remap(frame, new_x.astype(np.float32), new_y.astype(np.float32), cv2.INTER_LINEAR)

        return warped

    def apply_fisheye_distortion(self, frame: np.ndarray, strength: float = 0.5) -> np.ndarray:
        """
        Apply fisheye lens distortion.

        Args:
            frame: Input frame
            strength: Distortion strength

        Returns:
            Fisheye-distorted frame
        """
        height, width = frame.shape[:2]
        center_x, center_y = width // 2, height // 2

        # Create coordinate grids
        y, x = np.ogrid[:height, :width]

        # Calculate distance from center
        dx = (x - center_x) / center_x
        dy = (y - center_y) / center_y
        distance = np.sqrt(dx*dx + dy*dy)

        # Apply fisheye distortion
        r = distance
        theta = np.arctan2(dy, dx)

        # Fisheye transformation
        r_new = r * (1 + strength * r * r)

        # Convert back to Cartesian
        new_x = center_x + r_new * center_x * np.cos(theta)
        new_y = center_y + r_new * center_y * np.sin(theta)

        # Clamp coordinates
        new_x = np.clip(new_x, 0, width - 1)
        new_y = np.clip(new_y, 0, height - 1)

        # Apply distortion
        warped = cv2.remap(frame, new_x.astype(np.float32), new_y.astype(np.float32), cv2.INTER_LINEAR)

        return warped

    def apply_recursive_zoom(self, frame: np.ndarray, zoom_factor: float = 1.1,
                           iterations: int = None) -> np.ndarray:
        """
        Apply recursive zoom effect.

        Args:
            frame: Input frame
            zoom_factor: Zoom factor per iteration
            iterations: Number of recursive iterations

        Returns:
            Recursively zoomed frame
        """
        iterations = iterations or self.recursive_depth
        result = frame.copy()

        height, width = frame.shape[:2]
        center_x, center_y = width // 2, height // 2

        for i in range(iterations):
            # Calculate zoom
            scale = zoom_factor ** (i + 1)

            # Calculate new dimensions
            new_width = int(width / scale)
            new_height = int(height / scale)

            if new_width < 10 or new_height < 10:
                break

            # Extract center region
            x1 = center_x - new_width // 2
            y1 = center_y - new_height // 2
            x2 = x1 + new_width
            y2 = y1 + new_height

            # Ensure bounds
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(width, x2)
            y2 = min(height, y2)

            if x2 > x1 and y2 > y1:
                # Extract and resize
                cropped = result[y1:y2, x1:x2]
                resized = cv2.resize(cropped, (width, height))

                # Blend with original
                alpha = 0.3 / (i + 1)  # Decreasing alpha
                result = cv2.addWeighted(result, 1 - alpha, resized, alpha, 0)

        return result

    def apply_kaleidoscope(self, frame: np.ndarray, segments: int = 6) -> np.ndarray:
        """
        Apply kaleidoscope effect.

        Args:
            frame: Input frame
            segments: Number of kaleidoscope segments

        Returns:
            Kaleidoscope effect frame
        """
        height, width = frame.shape[:2]
        center_x, center_y = width // 2, height // 2

        # Create result frame
        result = np.zeros_like(frame)

        # Calculate segment angle
        segment_angle = 2 * np.pi / segments

        for i in range(segments):
            # Calculate rotation angle
            angle = i * segment_angle

            # Create rotation matrix
            M = cv2.getRotationMatrix2D((center_x, center_y), np.degrees(angle), 1.0)

            # Rotate frame
            rotated = cv2.warpAffine(frame, M, (width, height))

            # Create mask for this segment
            mask = self._create_segment_mask(width, height, center_x, center_y,
                                           angle, segment_angle)

            # Apply mask and add to result
            masked = cv2.bitwise_and(rotated, rotated, mask=mask)
            result = cv2.add(result, masked)

        return result

    def _create_segment_mask(self, width: int, height: int, center_x: int, center_y: int,
                           start_angle: float, segment_angle: float) -> np.ndarray:
        """Create a mask for kaleidoscope segment."""
        mask = np.zeros((height, width), dtype=np.uint8)

        # Create points for the segment
        radius = max(width, height)

        points = [
            [center_x, center_y],
            [center_x + radius * np.cos(start_angle), center_y + radius * np.sin(start_angle)],
            [center_x + radius * np.cos(start_angle + segment_angle),
             center_y + radius * np.sin(start_angle + segment_angle)]
        ]

        points = np.array(points, dtype=np.int32)
        cv2.fillPoly(mask, [points], 255)

        return mask

    def apply_mirror_effect(self, frame: np.ndarray, direction: str = "horizontal") -> np.ndarray:
        """
        Apply mirror effect.

        Args:
            frame: Input frame
            direction: "horizontal", "vertical", "both", or "diagonal"

        Returns:
            Mirrored frame
        """
        height, width = frame.shape[:2]

        if direction == "horizontal":
            left_half = frame[:, :width//2]
            mirrored = cv2.flip(left_half, 1)
            result = np.hstack([left_half, mirrored])
        elif direction == "vertical":
            top_half = frame[:height//2, :]
            mirrored = cv2.flip(top_half, 0)
            result = np.vstack([top_half, mirrored])
        elif direction == "both":
            quarter = frame[:height//2, :width//2]
            top_right = cv2.flip(quarter, 1)
            bottom_left = cv2.flip(quarter, 0)
            bottom_right = cv2.flip(quarter, -1)

            top = np.hstack([quarter, top_right])
            bottom = np.hstack([bottom_left, bottom_right])
            result = np.vstack([top, bottom])
        elif direction == "diagonal":
            # Diagonal mirror effect
            result = frame.copy()
            for i in range(min(height, width)):
                for j in range(i):
                    if i < height and j < width:
                        result[i, j] = frame[j, i]
        else:
            result = frame.copy()

        return result

    def apply_pixelation(self, frame: np.ndarray, pixel_size: int = 10) -> np.ndarray:
        """
        Apply pixelation effect.

        Args:
            frame: Input frame
            pixel_size: Size of pixels

        Returns:
            Pixelated frame
        """
        height, width = frame.shape[:2]

        # Downsample
        small_height = height // pixel_size
        small_width = width // pixel_size

        if small_height > 0 and small_width > 0:
            small = cv2.resize(frame, (small_width, small_height), interpolation=cv2.INTER_NEAREST)
            # Upsample back
            pixelated = cv2.resize(small, (width, height), interpolation=cv2.INTER_NEAREST)
            return pixelated

        return frame

    def apply_fractal_zoom(self, frame: np.ndarray, zoom_points: List[Tuple[float, float]] = None) -> np.ndarray:
        """
        Apply fractal-like zoom effect with multiple zoom points.

        Args:
            frame: Input frame
            zoom_points: List of (x, y) zoom center points (normalized 0-1)

        Returns:
            Fractal zoom effect frame
        """
        if zoom_points is None:
            zoom_points = [(0.5, 0.5), (0.3, 0.7), (0.7, 0.3)]

        height, width = frame.shape[:2]
        result = frame.copy().astype(np.float32)

        for i, (norm_x, norm_y) in enumerate(zoom_points):
            center_x = int(norm_x * width)
            center_y = int(norm_y * height)

            # Create zoom effect around this point
            zoom_factor = 1.5 + i * 0.3

            # Calculate crop region
            crop_size = int(min(width, height) / zoom_factor)
            x1 = max(0, center_x - crop_size // 2)
            y1 = max(0, center_y - crop_size // 2)
            x2 = min(width, x1 + crop_size)
            y2 = min(height, y1 + crop_size)

            if x2 > x1 and y2 > y1:
                # Extract and resize
                cropped = frame[y1:y2, x1:x2]
                resized = cv2.resize(cropped, (width, height))

                # Blend with result
                alpha = 0.2 / (i + 1)
                result = cv2.addWeighted(result, 1 - alpha, resized.astype(np.float32), alpha, 0)

        return np.clip(result, 0, 255).astype(np.uint8)

    def apply_liquid_distortion(self, frame: np.ndarray, viscosity: float = 0.5,
                               flow_direction: float = 0.0, turbulence: float = 0.3,
                               time_factor: float = 0.0) -> np.ndarray:
        """
        Apply liquid-like distortion effect.

        Args:
            frame: Input frame
            viscosity: Liquid viscosity (0.0-1.0, higher = more fluid)
            flow_direction: Flow direction in radians
            turbulence: Turbulence amount (0.0-1.0)
            time_factor: Time-based animation factor

        Returns:
            Liquid-distorted frame
        """
        height, width = frame.shape[:2]

        # Create coordinate grids
        y, x = np.ogrid[:height, :width]

        # Normalize coordinates
        x_norm = x / width
        y_norm = y / height

        # Create flow field
        flow_x = np.cos(flow_direction) * viscosity
        flow_y = np.sin(flow_direction) * viscosity

        # Add turbulence
        turbulence_x = turbulence * np.sin(4 * np.pi * x_norm + time_factor) * np.cos(3 * np.pi * y_norm)
        turbulence_y = turbulence * np.cos(3 * np.pi * x_norm) * np.sin(4 * np.pi * y_norm + time_factor)

        # Combine flow and turbulence
        displacement_x = (flow_x + turbulence_x) * width * 0.1
        displacement_y = (flow_y + turbulence_y) * height * 0.1

        # Apply displacement
        new_x = x + displacement_x
        new_y = y + displacement_y

        # Clamp coordinates
        new_x = np.clip(new_x, 0, width - 1)
        new_y = np.clip(new_y, 0, height - 1)

        # Apply distortion
        result = cv2.remap(frame, new_x.astype(np.float32), new_y.astype(np.float32), cv2.INTER_LINEAR)

        return result

    def apply_vortex_distortion(self, frame: np.ndarray, center_x: float = 0.5, center_y: float = 0.5,
                               strength: float = 1.0, radius: float = 0.5, clockwise: bool = True) -> np.ndarray:
        """
        Apply vortex/whirlpool distortion effect.

        Args:
            frame: Input frame
            center_x: Vortex center X (normalized 0-1)
            center_y: Vortex center Y (normalized 0-1)
            strength: Vortex strength
            radius: Vortex radius (normalized 0-1)
            clockwise: Rotation direction

        Returns:
            Vortex-distorted frame
        """
        height, width = frame.shape[:2]
        center_px = int(center_x * width)
        center_py = int(center_y * height)
        max_radius = radius * min(width, height) / 2

        # Create coordinate grids
        y, x = np.ogrid[:height, :width]

        # Calculate distance and angle from center
        dx = x - center_px
        dy = y - center_py
        distance = np.sqrt(dx*dx + dy*dy)
        angle = np.arctan2(dy, dx)

        # Apply vortex effect within radius
        mask = distance <= max_radius
        vortex_strength = np.where(mask, strength * (1 - distance / max_radius), 0)

        # Rotate based on distance
        rotation_angle = vortex_strength * (np.pi if clockwise else -np.pi)
        new_angle = angle + rotation_angle

        # Convert back to Cartesian coordinates
        new_x = center_px + distance * np.cos(new_angle)
        new_y = center_py + distance * np.sin(new_angle)

        # Use original coordinates outside vortex
        new_x = np.where(mask, new_x, x)
        new_y = np.where(mask, new_y, y)

        # Clamp coordinates
        new_x = np.clip(new_x, 0, width - 1)
        new_y = np.clip(new_y, 0, height - 1)

        # Apply distortion
        result = cv2.remap(frame, new_x.astype(np.float32), new_y.astype(np.float32), cv2.INTER_LINEAR)

        return result

    def apply_perspective_warp(self, frame: np.ndarray, corners: List[Tuple[float, float]] = None,
                              auto_perspective: bool = True) -> np.ndarray:
        """
        Apply perspective warping effect.

        Args:
            frame: Input frame
            corners: Four corner points as (x, y) normalized coordinates
            auto_perspective: Use automatic perspective calculation

        Returns:
            Perspective-warped frame
        """
        height, width = frame.shape[:2]

        if corners is None or auto_perspective:
            # Generate random perspective corners
            margin = 0.1
            corners = [
                (random.uniform(margin, 1-margin), random.uniform(margin, 1-margin)),
                (random.uniform(margin, 1-margin), random.uniform(margin, 1-margin)),
                (random.uniform(margin, 1-margin), random.uniform(margin, 1-margin)),
                (random.uniform(margin, 1-margin), random.uniform(margin, 1-margin))
            ]

        # Convert normalized coordinates to pixel coordinates
        src_points = np.float32([
            [0, 0],
            [width, 0],
            [width, height],
            [0, height]
        ])

        dst_points = np.float32([
            [corners[0][0] * width, corners[0][1] * height],
            [corners[1][0] * width, corners[1][1] * height],
            [corners[2][0] * width, corners[2][1] * height],
            [corners[3][0] * width, corners[3][1] * height]
        ])

        # Calculate perspective transformation matrix
        M = cv2.getPerspectiveTransform(src_points, dst_points)

        # Apply perspective warp
        result = cv2.warpPerspective(frame, M, (width, height))

        return result
